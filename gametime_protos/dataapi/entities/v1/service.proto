syntax = "proto3";

package dataapi.entities.v1;

option go_package = "dataapi/entities/v1;protos";

import "google/protobuf/struct.proto";
import "google/protobuf/any.proto";

service GenericService {
  rpc GetGenericEntities(GetGenericEntitiesRequest) returns (GetGenericEntitiesResponse);
  rpc SetGenericEntities(SetGenericEntitiesRequest) returns (SetGenericEntitiesResponse);
}

message Entity {
  google.protobuf.Struct fields = 1;
}

message GetGenericEntitiesRequest {
  string key = 1;
}

message GetGenericEntitiesResponse {
  repeated Entity entities = 1;
}

message SetGenericEntitiesRequest {
  string key = 1;
  repeated Entity entities = 2;
  int64 ttl_in_seconds = 3;
}

message SetGenericEntitiesResponse {
  int32 code = 1;
  string message = 2;
  repeated google.protobuf.Any details = 3;
}
