syntax = "proto3";

package mle.estimated_delivery.v1;

option go_package = "mle/estimated_delivery/v1;protos";

// Define a new service for estimated ticket delivery
service EstimatedTicketDeliveryService {
  // GetEstimatedDelivery returns the estimated delivery time for a ticket.
  rpc GetEstimatedDelivery(GetEstimatedDeliveryRequest) returns (GetEstimatedDeliveryResponse);
}

// Define a request message for the new service
message GetEstimatedDeliveryRequest {
  string ticket_id = 1; // Temp field for now until service is ready
}

// Define a response message for the new service
message GetEstimatedDeliveryResponse {
  int32 estimated_delivery_time = 1; // Temp field for now until service is ready
}

