// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: gametime_protos/dataapi/entities/v1/service.proto

package protos

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	anypb "google.golang.org/protobuf/types/known/anypb"
	structpb "google.golang.org/protobuf/types/known/structpb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Entity struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Fields        *structpb.Struct       `protobuf:"bytes,1,opt,name=fields,proto3" json:"fields,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Entity) Reset() {
	*x = Entity{}
	mi := &file_gametime_protos_dataapi_entities_v1_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Entity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Entity) ProtoMessage() {}

func (x *Entity) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_dataapi_entities_v1_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Entity.ProtoReflect.Descriptor instead.
func (*Entity) Descriptor() ([]byte, []int) {
	return file_gametime_protos_dataapi_entities_v1_service_proto_rawDescGZIP(), []int{0}
}

func (x *Entity) GetFields() *structpb.Struct {
	if x != nil {
		return x.Fields
	}
	return nil
}

type GetGenericEntitiesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Key           string                 `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetGenericEntitiesRequest) Reset() {
	*x = GetGenericEntitiesRequest{}
	mi := &file_gametime_protos_dataapi_entities_v1_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetGenericEntitiesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGenericEntitiesRequest) ProtoMessage() {}

func (x *GetGenericEntitiesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_dataapi_entities_v1_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGenericEntitiesRequest.ProtoReflect.Descriptor instead.
func (*GetGenericEntitiesRequest) Descriptor() ([]byte, []int) {
	return file_gametime_protos_dataapi_entities_v1_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetGenericEntitiesRequest) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

type GetGenericEntitiesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Entities      []*Entity              `protobuf:"bytes,1,rep,name=entities,proto3" json:"entities,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetGenericEntitiesResponse) Reset() {
	*x = GetGenericEntitiesResponse{}
	mi := &file_gametime_protos_dataapi_entities_v1_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetGenericEntitiesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGenericEntitiesResponse) ProtoMessage() {}

func (x *GetGenericEntitiesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_dataapi_entities_v1_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGenericEntitiesResponse.ProtoReflect.Descriptor instead.
func (*GetGenericEntitiesResponse) Descriptor() ([]byte, []int) {
	return file_gametime_protos_dataapi_entities_v1_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetGenericEntitiesResponse) GetEntities() []*Entity {
	if x != nil {
		return x.Entities
	}
	return nil
}

type SetGenericEntitiesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Key           string                 `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Entities      []*Entity              `protobuf:"bytes,2,rep,name=entities,proto3" json:"entities,omitempty"`
	TtlInSeconds  int64                  `protobuf:"varint,3,opt,name=ttl_in_seconds,json=ttlInSeconds,proto3" json:"ttl_in_seconds,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetGenericEntitiesRequest) Reset() {
	*x = SetGenericEntitiesRequest{}
	mi := &file_gametime_protos_dataapi_entities_v1_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetGenericEntitiesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetGenericEntitiesRequest) ProtoMessage() {}

func (x *SetGenericEntitiesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_dataapi_entities_v1_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetGenericEntitiesRequest.ProtoReflect.Descriptor instead.
func (*SetGenericEntitiesRequest) Descriptor() ([]byte, []int) {
	return file_gametime_protos_dataapi_entities_v1_service_proto_rawDescGZIP(), []int{3}
}

func (x *SetGenericEntitiesRequest) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *SetGenericEntitiesRequest) GetEntities() []*Entity {
	if x != nil {
		return x.Entities
	}
	return nil
}

func (x *SetGenericEntitiesRequest) GetTtlInSeconds() int64 {
	if x != nil {
		return x.TtlInSeconds
	}
	return 0
}

type SetGenericEntitiesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Details       []*anypb.Any           `protobuf:"bytes,3,rep,name=details,proto3" json:"details,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetGenericEntitiesResponse) Reset() {
	*x = SetGenericEntitiesResponse{}
	mi := &file_gametime_protos_dataapi_entities_v1_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetGenericEntitiesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetGenericEntitiesResponse) ProtoMessage() {}

func (x *SetGenericEntitiesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_dataapi_entities_v1_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetGenericEntitiesResponse.ProtoReflect.Descriptor instead.
func (*SetGenericEntitiesResponse) Descriptor() ([]byte, []int) {
	return file_gametime_protos_dataapi_entities_v1_service_proto_rawDescGZIP(), []int{4}
}

func (x *SetGenericEntitiesResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *SetGenericEntitiesResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *SetGenericEntitiesResponse) GetDetails() []*anypb.Any {
	if x != nil {
		return x.Details
	}
	return nil
}

var File_gametime_protos_dataapi_entities_v1_service_proto protoreflect.FileDescriptor

const file_gametime_protos_dataapi_entities_v1_service_proto_rawDesc = "" +
	"\n" +
	"1gametime_protos/dataapi/entities/v1/service.proto\x12\x13dataapi.entities.v1\x1a\x1cgoogle/protobuf/struct.proto\x1a\x19google/protobuf/any.proto\"9\n" +
	"\x06Entity\x12/\n" +
	"\x06fields\x18\x01 \x01(\v2\x17.google.protobuf.StructR\x06fields\"-\n" +
	"\x19GetGenericEntitiesRequest\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\"U\n" +
	"\x1aGetGenericEntitiesResponse\x127\n" +
	"\bentities\x18\x01 \x03(\v2\x1b.dataapi.entities.v1.EntityR\bentities\"\x8c\x01\n" +
	"\x19SetGenericEntitiesRequest\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x127\n" +
	"\bentities\x18\x02 \x03(\v2\x1b.dataapi.entities.v1.EntityR\bentities\x12$\n" +
	"\x0ettl_in_seconds\x18\x03 \x01(\x03R\fttlInSeconds\"z\n" +
	"\x1aSetGenericEntitiesResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12.\n" +
	"\adetails\x18\x03 \x03(\v2\x14.google.protobuf.AnyR\adetails2\xfe\x01\n" +
	"\x0eGenericService\x12u\n" +
	"\x12GetGenericEntities\x12..dataapi.entities.v1.GetGenericEntitiesRequest\x1a/.dataapi.entities.v1.GetGenericEntitiesResponse\x12u\n" +
	"\x12SetGenericEntities\x12..dataapi.entities.v1.SetGenericEntitiesRequest\x1a/.dataapi.entities.v1.SetGenericEntitiesResponseB\xb1\x01\n" +
	"\x17com.dataapi.entities.v1B\fServiceProtoP\x01Z\x1adataapi/entities/v1;protos\xa2\x02\x03DEX\xaa\x02\x13Dataapi.Entities.V1\xca\x02\x13Dataapi\\Entities\\V1\xe2\x02\x1fDataapi\\Entities\\V1\\GPBMetadata\xea\x02\x15Dataapi::Entities::V1b\x06proto3"

var (
	file_gametime_protos_dataapi_entities_v1_service_proto_rawDescOnce sync.Once
	file_gametime_protos_dataapi_entities_v1_service_proto_rawDescData []byte
)

func file_gametime_protos_dataapi_entities_v1_service_proto_rawDescGZIP() []byte {
	file_gametime_protos_dataapi_entities_v1_service_proto_rawDescOnce.Do(func() {
		file_gametime_protos_dataapi_entities_v1_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_gametime_protos_dataapi_entities_v1_service_proto_rawDesc), len(file_gametime_protos_dataapi_entities_v1_service_proto_rawDesc)))
	})
	return file_gametime_protos_dataapi_entities_v1_service_proto_rawDescData
}

var file_gametime_protos_dataapi_entities_v1_service_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_gametime_protos_dataapi_entities_v1_service_proto_goTypes = []any{
	(*Entity)(nil),                     // 0: dataapi.entities.v1.Entity
	(*GetGenericEntitiesRequest)(nil),  // 1: dataapi.entities.v1.GetGenericEntitiesRequest
	(*GetGenericEntitiesResponse)(nil), // 2: dataapi.entities.v1.GetGenericEntitiesResponse
	(*SetGenericEntitiesRequest)(nil),  // 3: dataapi.entities.v1.SetGenericEntitiesRequest
	(*SetGenericEntitiesResponse)(nil), // 4: dataapi.entities.v1.SetGenericEntitiesResponse
	(*structpb.Struct)(nil),            // 5: google.protobuf.Struct
	(*anypb.Any)(nil),                  // 6: google.protobuf.Any
}
var file_gametime_protos_dataapi_entities_v1_service_proto_depIdxs = []int32{
	5, // 0: dataapi.entities.v1.Entity.fields:type_name -> google.protobuf.Struct
	0, // 1: dataapi.entities.v1.GetGenericEntitiesResponse.entities:type_name -> dataapi.entities.v1.Entity
	0, // 2: dataapi.entities.v1.SetGenericEntitiesRequest.entities:type_name -> dataapi.entities.v1.Entity
	6, // 3: dataapi.entities.v1.SetGenericEntitiesResponse.details:type_name -> google.protobuf.Any
	1, // 4: dataapi.entities.v1.GenericService.GetGenericEntities:input_type -> dataapi.entities.v1.GetGenericEntitiesRequest
	3, // 5: dataapi.entities.v1.GenericService.SetGenericEntities:input_type -> dataapi.entities.v1.SetGenericEntitiesRequest
	2, // 6: dataapi.entities.v1.GenericService.GetGenericEntities:output_type -> dataapi.entities.v1.GetGenericEntitiesResponse
	4, // 7: dataapi.entities.v1.GenericService.SetGenericEntities:output_type -> dataapi.entities.v1.SetGenericEntitiesResponse
	6, // [6:8] is the sub-list for method output_type
	4, // [4:6] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_gametime_protos_dataapi_entities_v1_service_proto_init() }
func file_gametime_protos_dataapi_entities_v1_service_proto_init() {
	if File_gametime_protos_dataapi_entities_v1_service_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_gametime_protos_dataapi_entities_v1_service_proto_rawDesc), len(file_gametime_protos_dataapi_entities_v1_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_gametime_protos_dataapi_entities_v1_service_proto_goTypes,
		DependencyIndexes: file_gametime_protos_dataapi_entities_v1_service_proto_depIdxs,
		MessageInfos:      file_gametime_protos_dataapi_entities_v1_service_proto_msgTypes,
	}.Build()
	File_gametime_protos_dataapi_entities_v1_service_proto = out.File
	file_gametime_protos_dataapi_entities_v1_service_proto_goTypes = nil
	file_gametime_protos_dataapi_entities_v1_service_proto_depIdxs = nil
}
