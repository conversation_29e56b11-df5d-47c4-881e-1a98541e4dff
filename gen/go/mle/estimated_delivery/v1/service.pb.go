// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: gametime_protos/mle/estimated_ticket_delivery/v1/service.proto

package protos

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Define a request message for the new service
type GetEstimatedDeliveryRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TicketId      string                 `protobuf:"bytes,1,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id,omitempty"` // Temp field for now until service is ready
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetEstimatedDeliveryRequest) Reset() {
	*x = GetEstimatedDeliveryRequest{}
	mi := &file_gametime_protos_mle_estimated_ticket_delivery_v1_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetEstimatedDeliveryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEstimatedDeliveryRequest) ProtoMessage() {}

func (x *GetEstimatedDeliveryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mle_estimated_ticket_delivery_v1_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEstimatedDeliveryRequest.ProtoReflect.Descriptor instead.
func (*GetEstimatedDeliveryRequest) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mle_estimated_ticket_delivery_v1_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetEstimatedDeliveryRequest) GetTicketId() string {
	if x != nil {
		return x.TicketId
	}
	return ""
}

// Define a response message for the new service
type GetEstimatedDeliveryResponse struct {
	state                 protoimpl.MessageState `protogen:"open.v1"`
	EstimatedDeliveryTime int32                  `protobuf:"varint,1,opt,name=estimated_delivery_time,json=estimatedDeliveryTime,proto3" json:"estimated_delivery_time,omitempty"` // Temp field for now until service is ready
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *GetEstimatedDeliveryResponse) Reset() {
	*x = GetEstimatedDeliveryResponse{}
	mi := &file_gametime_protos_mle_estimated_ticket_delivery_v1_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetEstimatedDeliveryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEstimatedDeliveryResponse) ProtoMessage() {}

func (x *GetEstimatedDeliveryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mle_estimated_ticket_delivery_v1_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEstimatedDeliveryResponse.ProtoReflect.Descriptor instead.
func (*GetEstimatedDeliveryResponse) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mle_estimated_ticket_delivery_v1_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetEstimatedDeliveryResponse) GetEstimatedDeliveryTime() int32 {
	if x != nil {
		return x.EstimatedDeliveryTime
	}
	return 0
}

var File_gametime_protos_mle_estimated_ticket_delivery_v1_service_proto protoreflect.FileDescriptor

const file_gametime_protos_mle_estimated_ticket_delivery_v1_service_proto_rawDesc = "" +
	"\n" +
	">gametime_protos/mle/estimated_ticket_delivery/v1/service.proto\x12\x19mle.estimated_delivery.v1\":\n" +
	"\x1bGetEstimatedDeliveryRequest\x12\x1b\n" +
	"\tticket_id\x18\x01 \x01(\tR\bticketId\"V\n" +
	"\x1cGetEstimatedDeliveryResponse\x126\n" +
	"\x17estimated_delivery_time\x18\x01 \x01(\x05R\x15estimatedDeliveryTime2\xaa\x01\n" +
	"\x1eEstimatedTicketDeliveryService\x12\x87\x01\n" +
	"\x14GetEstimatedDelivery\x126.mle.estimated_delivery.v1.GetEstimatedDeliveryRequest\x1a7.mle.estimated_delivery.v1.GetEstimatedDeliveryResponseB\xd1\x01\n" +
	"\x1dcom.mle.estimated_delivery.v1B\fServiceProtoP\x01Z mle/estimated_delivery/v1;protos\xa2\x02\x03MEX\xaa\x02\x18Mle.EstimatedDelivery.V1\xca\x02\x18Mle\\EstimatedDelivery\\V1\xe2\x02$Mle\\EstimatedDelivery\\V1\\GPBMetadata\xea\x02\x1aMle::EstimatedDelivery::V1b\x06proto3"

var (
	file_gametime_protos_mle_estimated_ticket_delivery_v1_service_proto_rawDescOnce sync.Once
	file_gametime_protos_mle_estimated_ticket_delivery_v1_service_proto_rawDescData []byte
)

func file_gametime_protos_mle_estimated_ticket_delivery_v1_service_proto_rawDescGZIP() []byte {
	file_gametime_protos_mle_estimated_ticket_delivery_v1_service_proto_rawDescOnce.Do(func() {
		file_gametime_protos_mle_estimated_ticket_delivery_v1_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_gametime_protos_mle_estimated_ticket_delivery_v1_service_proto_rawDesc), len(file_gametime_protos_mle_estimated_ticket_delivery_v1_service_proto_rawDesc)))
	})
	return file_gametime_protos_mle_estimated_ticket_delivery_v1_service_proto_rawDescData
}

var file_gametime_protos_mle_estimated_ticket_delivery_v1_service_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_gametime_protos_mle_estimated_ticket_delivery_v1_service_proto_goTypes = []any{
	(*GetEstimatedDeliveryRequest)(nil),  // 0: mle.estimated_delivery.v1.GetEstimatedDeliveryRequest
	(*GetEstimatedDeliveryResponse)(nil), // 1: mle.estimated_delivery.v1.GetEstimatedDeliveryResponse
}
var file_gametime_protos_mle_estimated_ticket_delivery_v1_service_proto_depIdxs = []int32{
	0, // 0: mle.estimated_delivery.v1.EstimatedTicketDeliveryService.GetEstimatedDelivery:input_type -> mle.estimated_delivery.v1.GetEstimatedDeliveryRequest
	1, // 1: mle.estimated_delivery.v1.EstimatedTicketDeliveryService.GetEstimatedDelivery:output_type -> mle.estimated_delivery.v1.GetEstimatedDeliveryResponse
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_gametime_protos_mle_estimated_ticket_delivery_v1_service_proto_init() }
func file_gametime_protos_mle_estimated_ticket_delivery_v1_service_proto_init() {
	if File_gametime_protos_mle_estimated_ticket_delivery_v1_service_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_gametime_protos_mle_estimated_ticket_delivery_v1_service_proto_rawDesc), len(file_gametime_protos_mle_estimated_ticket_delivery_v1_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_gametime_protos_mle_estimated_ticket_delivery_v1_service_proto_goTypes,
		DependencyIndexes: file_gametime_protos_mle_estimated_ticket_delivery_v1_service_proto_depIdxs,
		MessageInfos:      file_gametime_protos_mle_estimated_ticket_delivery_v1_service_proto_msgTypes,
	}.Build()
	File_gametime_protos_mle_estimated_ticket_delivery_v1_service_proto = out.File
	file_gametime_protos_mle_estimated_ticket_delivery_v1_service_proto_goTypes = nil
	file_gametime_protos_mle_estimated_ticket_delivery_v1_service_proto_depIdxs = nil
}
