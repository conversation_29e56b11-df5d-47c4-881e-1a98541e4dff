// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: gametime_protos/mle/estimated_ticket_delivery/v1/service.proto

package protos

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	EstimatedTicketDeliveryService_GetEstimatedDelivery_FullMethodName = "/mle.estimated_delivery.v1.EstimatedTicketDeliveryService/GetEstimatedDelivery"
)

// EstimatedTicketDeliveryServiceClient is the client API for EstimatedTicketDeliveryService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// Define a new service for estimated ticket delivery
type EstimatedTicketDeliveryServiceClient interface {
	// GetEstimatedDelivery returns the estimated delivery time for a ticket.
	GetEstimatedDelivery(ctx context.Context, in *GetEstimatedDeliveryRequest, opts ...grpc.CallOption) (*GetEstimatedDeliveryResponse, error)
}

type estimatedTicketDeliveryServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewEstimatedTicketDeliveryServiceClient(cc grpc.ClientConnInterface) EstimatedTicketDeliveryServiceClient {
	return &estimatedTicketDeliveryServiceClient{cc}
}

func (c *estimatedTicketDeliveryServiceClient) GetEstimatedDelivery(ctx context.Context, in *GetEstimatedDeliveryRequest, opts ...grpc.CallOption) (*GetEstimatedDeliveryResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetEstimatedDeliveryResponse)
	err := c.cc.Invoke(ctx, EstimatedTicketDeliveryService_GetEstimatedDelivery_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// EstimatedTicketDeliveryServiceServer is the server API for EstimatedTicketDeliveryService service.
// All implementations must embed UnimplementedEstimatedTicketDeliveryServiceServer
// for forward compatibility.
//
// Define a new service for estimated ticket delivery
type EstimatedTicketDeliveryServiceServer interface {
	// GetEstimatedDelivery returns the estimated delivery time for a ticket.
	GetEstimatedDelivery(context.Context, *GetEstimatedDeliveryRequest) (*GetEstimatedDeliveryResponse, error)
	mustEmbedUnimplementedEstimatedTicketDeliveryServiceServer()
}

// UnimplementedEstimatedTicketDeliveryServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedEstimatedTicketDeliveryServiceServer struct{}

func (UnimplementedEstimatedTicketDeliveryServiceServer) GetEstimatedDelivery(context.Context, *GetEstimatedDeliveryRequest) (*GetEstimatedDeliveryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEstimatedDelivery not implemented")
}
func (UnimplementedEstimatedTicketDeliveryServiceServer) mustEmbedUnimplementedEstimatedTicketDeliveryServiceServer() {
}
func (UnimplementedEstimatedTicketDeliveryServiceServer) testEmbeddedByValue() {}

// UnsafeEstimatedTicketDeliveryServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to EstimatedTicketDeliveryServiceServer will
// result in compilation errors.
type UnsafeEstimatedTicketDeliveryServiceServer interface {
	mustEmbedUnimplementedEstimatedTicketDeliveryServiceServer()
}

func RegisterEstimatedTicketDeliveryServiceServer(s grpc.ServiceRegistrar, srv EstimatedTicketDeliveryServiceServer) {
	// If the following call pancis, it indicates UnimplementedEstimatedTicketDeliveryServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&EstimatedTicketDeliveryService_ServiceDesc, srv)
}

func _EstimatedTicketDeliveryService_GetEstimatedDelivery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEstimatedDeliveryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EstimatedTicketDeliveryServiceServer).GetEstimatedDelivery(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EstimatedTicketDeliveryService_GetEstimatedDelivery_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EstimatedTicketDeliveryServiceServer).GetEstimatedDelivery(ctx, req.(*GetEstimatedDeliveryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// EstimatedTicketDeliveryService_ServiceDesc is the grpc.ServiceDesc for EstimatedTicketDeliveryService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var EstimatedTicketDeliveryService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "mle.estimated_delivery.v1.EstimatedTicketDeliveryService",
	HandlerType: (*EstimatedTicketDeliveryServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetEstimatedDelivery",
			Handler:    _EstimatedTicketDeliveryService_GetEstimatedDelivery_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "gametime_protos/mle/estimated_ticket_delivery/v1/service.proto",
}
