// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: gametime_protos/mle/fraud/v1/service.proto

package protos

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	wrapperspb "google.golang.org/protobuf/types/known/wrapperspb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UserData struct {
	state                 protoimpl.MessageState  `protogen:"open.v1"`
	Id                    string                  `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	CreatedAt             *timestamppb.Timestamp  `protobuf:"bytes,2,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	Name                  *wrapperspb.StringValue `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Email                 string                  `protobuf:"bytes,4,opt,name=email,proto3" json:"email,omitempty"`
	Phone                 *wrapperspb.StringValue `protobuf:"bytes,5,opt,name=phone,proto3" json:"phone,omitempty"`
	SecondsSinceLastOrder *wrapperspb.Int64Value  `protobuf:"bytes,6,opt,name=seconds_since_last_order,json=secondsSinceLastOrder,proto3" json:"seconds_since_last_order,omitempty"`
	PaymentMethodCount    int32                   `protobuf:"varint,7,opt,name=payment_method_count,json=paymentMethodCount,proto3" json:"payment_method_count,omitempty"`
	OrderCount            int32                   `protobuf:"varint,8,opt,name=order_count,json=orderCount,proto3" json:"order_count,omitempty"`
	FirstName             *wrapperspb.StringValue `protobuf:"bytes,9,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	LastName              *wrapperspb.StringValue `protobuf:"bytes,10,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *UserData) Reset() {
	*x = UserData{}
	mi := &file_gametime_protos_mle_fraud_v1_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserData) ProtoMessage() {}

func (x *UserData) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mle_fraud_v1_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserData.ProtoReflect.Descriptor instead.
func (*UserData) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mle_fraud_v1_service_proto_rawDescGZIP(), []int{0}
}

func (x *UserData) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UserData) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *UserData) GetName() *wrapperspb.StringValue {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *UserData) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *UserData) GetPhone() *wrapperspb.StringValue {
	if x != nil {
		return x.Phone
	}
	return nil
}

func (x *UserData) GetSecondsSinceLastOrder() *wrapperspb.Int64Value {
	if x != nil {
		return x.SecondsSinceLastOrder
	}
	return nil
}

func (x *UserData) GetPaymentMethodCount() int32 {
	if x != nil {
		return x.PaymentMethodCount
	}
	return 0
}

func (x *UserData) GetOrderCount() int32 {
	if x != nil {
		return x.OrderCount
	}
	return 0
}

func (x *UserData) GetFirstName() *wrapperspb.StringValue {
	if x != nil {
		return x.FirstName
	}
	return nil
}

func (x *UserData) GetLastName() *wrapperspb.StringValue {
	if x != nil {
		return x.LastName
	}
	return nil
}

type DeviceData struct {
	state          protoimpl.MessageState  `protogen:"open.v1"`
	Id             string                  `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Manufacturer   string                  `protobuf:"bytes,2,opt,name=manufacturer,proto3" json:"manufacturer,omitempty"`
	Model          string                  `protobuf:"bytes,3,opt,name=model,proto3" json:"model,omitempty"`
	OsType         string                  `protobuf:"bytes,4,opt,name=os_type,json=osType,proto3" json:"os_type,omitempty"`
	OsVersion      string                  `protobuf:"bytes,5,opt,name=os_version,json=osVersion,proto3" json:"os_version,omitempty"`
	Platform       string                  `protobuf:"bytes,6,opt,name=platform,proto3" json:"platform,omitempty"`
	AppVersion     string                  `protobuf:"bytes,7,opt,name=app_version,json=appVersion,proto3" json:"app_version,omitempty"`
	AcceptLanguage *wrapperspb.StringValue `protobuf:"bytes,8,opt,name=accept_language,json=acceptLanguage,proto3" json:"accept_language,omitempty"`
	IpAddress      string                  `protobuf:"bytes,9,opt,name=ip_address,json=ipAddress,proto3" json:"ip_address,omitempty"`
	UserAgent      string                  `protobuf:"bytes,10,opt,name=user_agent,json=userAgent,proto3" json:"user_agent,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *DeviceData) Reset() {
	*x = DeviceData{}
	mi := &file_gametime_protos_mle_fraud_v1_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceData) ProtoMessage() {}

func (x *DeviceData) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mle_fraud_v1_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceData.ProtoReflect.Descriptor instead.
func (*DeviceData) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mle_fraud_v1_service_proto_rawDescGZIP(), []int{1}
}

func (x *DeviceData) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *DeviceData) GetManufacturer() string {
	if x != nil {
		return x.Manufacturer
	}
	return ""
}

func (x *DeviceData) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *DeviceData) GetOsType() string {
	if x != nil {
		return x.OsType
	}
	return ""
}

func (x *DeviceData) GetOsVersion() string {
	if x != nil {
		return x.OsVersion
	}
	return ""
}

func (x *DeviceData) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

func (x *DeviceData) GetAppVersion() string {
	if x != nil {
		return x.AppVersion
	}
	return ""
}

func (x *DeviceData) GetAcceptLanguage() *wrapperspb.StringValue {
	if x != nil {
		return x.AcceptLanguage
	}
	return nil
}

func (x *DeviceData) GetIpAddress() string {
	if x != nil {
		return x.IpAddress
	}
	return ""
}

func (x *DeviceData) GetUserAgent() string {
	if x != nil {
		return x.UserAgent
	}
	return ""
}

type AddressData struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Line1         *wrapperspb.StringValue `protobuf:"bytes,1,opt,name=line1,proto3" json:"line1,omitempty"`
	Line2         *wrapperspb.StringValue `protobuf:"bytes,2,opt,name=line2,proto3" json:"line2,omitempty"`
	City          *wrapperspb.StringValue `protobuf:"bytes,3,opt,name=city,proto3" json:"city,omitempty"`
	State         *wrapperspb.StringValue `protobuf:"bytes,4,opt,name=state,proto3" json:"state,omitempty"`
	PostalCode    *wrapperspb.StringValue `protobuf:"bytes,5,opt,name=postal_code,json=postalCode,proto3" json:"postal_code,omitempty"`
	CountryCode   *wrapperspb.StringValue `protobuf:"bytes,6,opt,name=country_code,json=countryCode,proto3" json:"country_code,omitempty"`
	FirstName     *wrapperspb.StringValue `protobuf:"bytes,7,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	LastName      *wrapperspb.StringValue `protobuf:"bytes,8,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddressData) Reset() {
	*x = AddressData{}
	mi := &file_gametime_protos_mle_fraud_v1_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddressData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddressData) ProtoMessage() {}

func (x *AddressData) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mle_fraud_v1_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddressData.ProtoReflect.Descriptor instead.
func (*AddressData) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mle_fraud_v1_service_proto_rawDescGZIP(), []int{2}
}

func (x *AddressData) GetLine1() *wrapperspb.StringValue {
	if x != nil {
		return x.Line1
	}
	return nil
}

func (x *AddressData) GetLine2() *wrapperspb.StringValue {
	if x != nil {
		return x.Line2
	}
	return nil
}

func (x *AddressData) GetCity() *wrapperspb.StringValue {
	if x != nil {
		return x.City
	}
	return nil
}

func (x *AddressData) GetState() *wrapperspb.StringValue {
	if x != nil {
		return x.State
	}
	return nil
}

func (x *AddressData) GetPostalCode() *wrapperspb.StringValue {
	if x != nil {
		return x.PostalCode
	}
	return nil
}

func (x *AddressData) GetCountryCode() *wrapperspb.StringValue {
	if x != nil {
		return x.CountryCode
	}
	return nil
}

func (x *AddressData) GetFirstName() *wrapperspb.StringValue {
	if x != nil {
		return x.FirstName
	}
	return nil
}

func (x *AddressData) GetLastName() *wrapperspb.StringValue {
	if x != nil {
		return x.LastName
	}
	return nil
}

type EventData struct {
	state                                     protoimpl.MessageState `protogen:"open.v1"`
	Id                                        string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Timestamp                                 *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	League                                    string                 `protobuf:"bytes,3,opt,name=league,proto3" json:"league,omitempty"`
	Address                                   *AddressData           `protobuf:"bytes,4,opt,name=address,proto3" json:"address,omitempty"`
	MarketSalesTrailingOverallCents           *wrapperspb.Int64Value `protobuf:"bytes,5,opt,name=market_sales_trailing_overall_cents,json=marketSalesTrailingOverallCents,proto3" json:"market_sales_trailing_overall_cents,omitempty"`
	MarketSalesTrailing_24HoursCents          *wrapperspb.Int64Value `protobuf:"bytes,6,opt,name=market_sales_trailing_24_hours_cents,json=marketSalesTrailing24HoursCents,proto3" json:"market_sales_trailing_24_hours_cents,omitempty"`
	PerformerMarketSalesTrailingOverallCents  *wrapperspb.Int64Value `protobuf:"bytes,7,opt,name=performer_market_sales_trailing_overall_cents,json=performerMarketSalesTrailingOverallCents,proto3" json:"performer_market_sales_trailing_overall_cents,omitempty"`
	PerformerMarketSalesTrailing_24HoursCents *wrapperspb.Int64Value `protobuf:"bytes,8,opt,name=performer_market_sales_trailing_24_hours_cents,json=performerMarketSalesTrailing24HoursCents,proto3" json:"performer_market_sales_trailing_24_hours_cents,omitempty"`
	Latitude                                  float32                `protobuf:"fixed32,9,opt,name=latitude,proto3" json:"latitude,omitempty"`
	Longitude                                 float32                `protobuf:"fixed32,10,opt,name=longitude,proto3" json:"longitude,omitempty"`
	unknownFields                             protoimpl.UnknownFields
	sizeCache                                 protoimpl.SizeCache
}

func (x *EventData) Reset() {
	*x = EventData{}
	mi := &file_gametime_protos_mle_fraud_v1_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EventData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EventData) ProtoMessage() {}

func (x *EventData) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mle_fraud_v1_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EventData.ProtoReflect.Descriptor instead.
func (*EventData) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mle_fraud_v1_service_proto_rawDescGZIP(), []int{3}
}

func (x *EventData) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *EventData) GetTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

func (x *EventData) GetLeague() string {
	if x != nil {
		return x.League
	}
	return ""
}

func (x *EventData) GetAddress() *AddressData {
	if x != nil {
		return x.Address
	}
	return nil
}

func (x *EventData) GetMarketSalesTrailingOverallCents() *wrapperspb.Int64Value {
	if x != nil {
		return x.MarketSalesTrailingOverallCents
	}
	return nil
}

func (x *EventData) GetMarketSalesTrailing_24HoursCents() *wrapperspb.Int64Value {
	if x != nil {
		return x.MarketSalesTrailing_24HoursCents
	}
	return nil
}

func (x *EventData) GetPerformerMarketSalesTrailingOverallCents() *wrapperspb.Int64Value {
	if x != nil {
		return x.PerformerMarketSalesTrailingOverallCents
	}
	return nil
}

func (x *EventData) GetPerformerMarketSalesTrailing_24HoursCents() *wrapperspb.Int64Value {
	if x != nil {
		return x.PerformerMarketSalesTrailing_24HoursCents
	}
	return nil
}

func (x *EventData) GetLatitude() float32 {
	if x != nil {
		return x.Latitude
	}
	return 0
}

func (x *EventData) GetLongitude() float32 {
	if x != nil {
		return x.Longitude
	}
	return 0
}

type OrderData struct {
	state        protoimpl.MessageState `protogen:"open.v1"`
	Id           string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	CreatedAt    *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	Quantity     int32                  `protobuf:"varint,3,opt,name=quantity,proto3" json:"quantity,omitempty"`
	DeliveryType string                 `protobuf:"bytes,4,opt,name=delivery_type,json=deliveryType,proto3" json:"delivery_type,omitempty"`
	// Deprecated: Marked as deprecated in gametime_protos/mle/fraud/v1/service.proto.
	TransferEmail      *wrapperspb.StringValue `protobuf:"bytes,5,opt,name=transfer_email,json=transferEmail,proto3" json:"transfer_email,omitempty"`
	AmountCents        int64                   `protobuf:"varint,6,opt,name=amount_cents,json=amountCents,proto3" json:"amount_cents,omitempty"`
	CreditsUsedCents   int64                   `protobuf:"varint,7,opt,name=credits_used_cents,json=creditsUsedCents,proto3" json:"credits_used_cents,omitempty"`
	FailedAttemptCount int32                   `protobuf:"varint,8,opt,name=failed_attempt_count,json=failedAttemptCount,proto3" json:"failed_attempt_count,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *OrderData) Reset() {
	*x = OrderData{}
	mi := &file_gametime_protos_mle_fraud_v1_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OrderData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderData) ProtoMessage() {}

func (x *OrderData) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mle_fraud_v1_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderData.ProtoReflect.Descriptor instead.
func (*OrderData) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mle_fraud_v1_service_proto_rawDescGZIP(), []int{4}
}

func (x *OrderData) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *OrderData) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *OrderData) GetQuantity() int32 {
	if x != nil {
		return x.Quantity
	}
	return 0
}

func (x *OrderData) GetDeliveryType() string {
	if x != nil {
		return x.DeliveryType
	}
	return ""
}

// Deprecated: Marked as deprecated in gametime_protos/mle/fraud/v1/service.proto.
func (x *OrderData) GetTransferEmail() *wrapperspb.StringValue {
	if x != nil {
		return x.TransferEmail
	}
	return nil
}

func (x *OrderData) GetAmountCents() int64 {
	if x != nil {
		return x.AmountCents
	}
	return 0
}

func (x *OrderData) GetCreditsUsedCents() int64 {
	if x != nil {
		return x.CreditsUsedCents
	}
	return 0
}

func (x *OrderData) GetFailedAttemptCount() int32 {
	if x != nil {
		return x.FailedAttemptCount
	}
	return 0
}

type TransferData struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Email         *wrapperspb.StringValue `protobuf:"bytes,1,opt,name=email,proto3" json:"email,omitempty"`
	FirstName     *wrapperspb.StringValue `protobuf:"bytes,2,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	LastName      *wrapperspb.StringValue `protobuf:"bytes,3,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
	Phone         *wrapperspb.StringValue `protobuf:"bytes,4,opt,name=phone,proto3" json:"phone,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TransferData) Reset() {
	*x = TransferData{}
	mi := &file_gametime_protos_mle_fraud_v1_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TransferData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransferData) ProtoMessage() {}

func (x *TransferData) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mle_fraud_v1_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransferData.ProtoReflect.Descriptor instead.
func (*TransferData) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mle_fraud_v1_service_proto_rawDescGZIP(), []int{5}
}

func (x *TransferData) GetEmail() *wrapperspb.StringValue {
	if x != nil {
		return x.Email
	}
	return nil
}

func (x *TransferData) GetFirstName() *wrapperspb.StringValue {
	if x != nil {
		return x.FirstName
	}
	return nil
}

func (x *TransferData) GetLastName() *wrapperspb.StringValue {
	if x != nil {
		return x.LastName
	}
	return nil
}

func (x *TransferData) GetPhone() *wrapperspb.StringValue {
	if x != nil {
		return x.Phone
	}
	return nil
}

type CardData struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Brand         string                  `protobuf:"bytes,1,opt,name=brand,proto3" json:"brand,omitempty"`
	Bin           string                  `protobuf:"bytes,2,opt,name=bin,proto3" json:"bin,omitempty"`
	Fingerprint   string                  `protobuf:"bytes,3,opt,name=fingerprint,proto3" json:"fingerprint,omitempty"`
	Funding       string                  `protobuf:"bytes,4,opt,name=funding,proto3" json:"funding,omitempty"`
	AvsCheck      *wrapperspb.StringValue `protobuf:"bytes,5,opt,name=avs_check,json=avsCheck,proto3" json:"avs_check,omitempty"`
	CvvCheck      *wrapperspb.StringValue `protobuf:"bytes,6,opt,name=cvv_check,json=cvvCheck,proto3" json:"cvv_check,omitempty"`
	Prepaid       bool                    `protobuf:"varint,7,opt,name=prepaid,proto3" json:"prepaid,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CardData) Reset() {
	*x = CardData{}
	mi := &file_gametime_protos_mle_fraud_v1_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CardData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CardData) ProtoMessage() {}

func (x *CardData) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mle_fraud_v1_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CardData.ProtoReflect.Descriptor instead.
func (*CardData) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mle_fraud_v1_service_proto_rawDescGZIP(), []int{6}
}

func (x *CardData) GetBrand() string {
	if x != nil {
		return x.Brand
	}
	return ""
}

func (x *CardData) GetBin() string {
	if x != nil {
		return x.Bin
	}
	return ""
}

func (x *CardData) GetFingerprint() string {
	if x != nil {
		return x.Fingerprint
	}
	return ""
}

func (x *CardData) GetFunding() string {
	if x != nil {
		return x.Funding
	}
	return ""
}

func (x *CardData) GetAvsCheck() *wrapperspb.StringValue {
	if x != nil {
		return x.AvsCheck
	}
	return nil
}

func (x *CardData) GetCvvCheck() *wrapperspb.StringValue {
	if x != nil {
		return x.CvvCheck
	}
	return nil
}

func (x *CardData) GetPrepaid() bool {
	if x != nil {
		return x.Prepaid
	}
	return false
}

type PaymentMethodData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	Gateway       string                 `protobuf:"bytes,3,opt,name=gateway,proto3" json:"gateway,omitempty"`
	Kind          string                 `protobuf:"bytes,4,opt,name=kind,proto3" json:"kind,omitempty"`
	Card          *CardData              `protobuf:"bytes,5,opt,name=card,proto3" json:"card,omitempty"`
	Address       *AddressData           `protobuf:"bytes,6,opt,name=address,proto3" json:"address,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PaymentMethodData) Reset() {
	*x = PaymentMethodData{}
	mi := &file_gametime_protos_mle_fraud_v1_service_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PaymentMethodData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PaymentMethodData) ProtoMessage() {}

func (x *PaymentMethodData) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mle_fraud_v1_service_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PaymentMethodData.ProtoReflect.Descriptor instead.
func (*PaymentMethodData) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mle_fraud_v1_service_proto_rawDescGZIP(), []int{7}
}

func (x *PaymentMethodData) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *PaymentMethodData) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *PaymentMethodData) GetGateway() string {
	if x != nil {
		return x.Gateway
	}
	return ""
}

func (x *PaymentMethodData) GetKind() string {
	if x != nil {
		return x.Kind
	}
	return ""
}

func (x *PaymentMethodData) GetCard() *CardData {
	if x != nil {
		return x.Card
	}
	return nil
}

func (x *PaymentMethodData) GetAddress() *AddressData {
	if x != nil {
		return x.Address
	}
	return nil
}

type EmailageData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Score         int32                  `protobuf:"varint,1,opt,name=score,proto3" json:"score,omitempty"`
	EmailAgeYears int32                  `protobuf:"varint,2,opt,name=email_age_years,json=emailAgeYears,proto3" json:"email_age_years,omitempty"`
	FraudLevel    string                 `protobuf:"bytes,3,opt,name=fraud_level,json=fraudLevel,proto3" json:"fraud_level,omitempty"`
	Reason        string                 `protobuf:"bytes,4,opt,name=reason,proto3" json:"reason,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EmailageData) Reset() {
	*x = EmailageData{}
	mi := &file_gametime_protos_mle_fraud_v1_service_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EmailageData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmailageData) ProtoMessage() {}

func (x *EmailageData) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mle_fraud_v1_service_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmailageData.ProtoReflect.Descriptor instead.
func (*EmailageData) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mle_fraud_v1_service_proto_rawDescGZIP(), []int{8}
}

func (x *EmailageData) GetScore() int32 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *EmailageData) GetEmailAgeYears() int32 {
	if x != nil {
		return x.EmailAgeYears
	}
	return 0
}

func (x *EmailageData) GetFraudLevel() string {
	if x != nil {
		return x.FraudLevel
	}
	return ""
}

func (x *EmailageData) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

type TelesignData struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	PhoneType      string                 `protobuf:"bytes,1,opt,name=phone_type,json=phoneType,proto3" json:"phone_type,omitempty"`
	Score          int32                  `protobuf:"varint,2,opt,name=score,proto3" json:"score,omitempty"`
	Risk           string                 `protobuf:"bytes,3,opt,name=risk,proto3" json:"risk,omitempty"`
	Recommendation string                 `protobuf:"bytes,4,opt,name=recommendation,proto3" json:"recommendation,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *TelesignData) Reset() {
	*x = TelesignData{}
	mi := &file_gametime_protos_mle_fraud_v1_service_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TelesignData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TelesignData) ProtoMessage() {}

func (x *TelesignData) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mle_fraud_v1_service_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TelesignData.ProtoReflect.Descriptor instead.
func (*TelesignData) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mle_fraud_v1_service_proto_rawDescGZIP(), []int{9}
}

func (x *TelesignData) GetPhoneType() string {
	if x != nil {
		return x.PhoneType
	}
	return ""
}

func (x *TelesignData) GetScore() int32 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *TelesignData) GetRisk() string {
	if x != nil {
		return x.Risk
	}
	return ""
}

func (x *TelesignData) GetRecommendation() string {
	if x != nil {
		return x.Recommendation
	}
	return ""
}

type FastlyData struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	PostalCode       string                 `protobuf:"bytes,1,opt,name=postal_code,json=postalCode,proto3" json:"postal_code,omitempty"`
	CountryCode      string                 `protobuf:"bytes,2,opt,name=country_code,json=countryCode,proto3" json:"country_code,omitempty"`
	Latitude         float32                `protobuf:"fixed32,3,opt,name=latitude,proto3" json:"latitude,omitempty"`
	Longitude        float32                `protobuf:"fixed32,4,opt,name=longitude,proto3" json:"longitude,omitempty"`
	ConnectionSpeed  string                 `protobuf:"bytes,5,opt,name=connection_speed,json=connectionSpeed,proto3" json:"connection_speed,omitempty"`
	ConnectionType   string                 `protobuf:"bytes,6,opt,name=connection_type,json=connectionType,proto3" json:"connection_type,omitempty"`
	ProxyType        string                 `protobuf:"bytes,7,opt,name=proxy_type,json=proxyType,proto3" json:"proxy_type,omitempty"`
	ProxyDescription string                 `protobuf:"bytes,8,opt,name=proxy_description,json=proxyDescription,proto3" json:"proxy_description,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *FastlyData) Reset() {
	*x = FastlyData{}
	mi := &file_gametime_protos_mle_fraud_v1_service_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FastlyData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FastlyData) ProtoMessage() {}

func (x *FastlyData) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mle_fraud_v1_service_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FastlyData.ProtoReflect.Descriptor instead.
func (*FastlyData) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mle_fraud_v1_service_proto_rawDescGZIP(), []int{10}
}

func (x *FastlyData) GetPostalCode() string {
	if x != nil {
		return x.PostalCode
	}
	return ""
}

func (x *FastlyData) GetCountryCode() string {
	if x != nil {
		return x.CountryCode
	}
	return ""
}

func (x *FastlyData) GetLatitude() float32 {
	if x != nil {
		return x.Latitude
	}
	return 0
}

func (x *FastlyData) GetLongitude() float32 {
	if x != nil {
		return x.Longitude
	}
	return 0
}

func (x *FastlyData) GetConnectionSpeed() string {
	if x != nil {
		return x.ConnectionSpeed
	}
	return ""
}

func (x *FastlyData) GetConnectionType() string {
	if x != nil {
		return x.ConnectionType
	}
	return ""
}

func (x *FastlyData) GetProxyType() string {
	if x != nil {
		return x.ProxyType
	}
	return ""
}

func (x *FastlyData) GetProxyDescription() string {
	if x != nil {
		return x.ProxyDescription
	}
	return ""
}

type FraudServiceScoreOrderRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	User          *UserData              `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	Device        *DeviceData            `protobuf:"bytes,2,opt,name=device,proto3" json:"device,omitempty"`
	Event         *EventData             `protobuf:"bytes,3,opt,name=event,proto3" json:"event,omitempty"`
	Order         *OrderData             `protobuf:"bytes,4,opt,name=order,proto3" json:"order,omitempty"`
	PaymentMethod *PaymentMethodData     `protobuf:"bytes,5,opt,name=payment_method,json=paymentMethod,proto3" json:"payment_method,omitempty"`
	Emailage      *EmailageData          `protobuf:"bytes,6,opt,name=emailage,proto3" json:"emailage,omitempty"`
	Telesign      *TelesignData          `protobuf:"bytes,7,opt,name=telesign,proto3" json:"telesign,omitempty"`
	Fastly        *FastlyData            `protobuf:"bytes,8,opt,name=fastly,proto3" json:"fastly,omitempty"`
	Transfer      *TransferData          `protobuf:"bytes,9,opt,name=transfer,proto3" json:"transfer,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FraudServiceScoreOrderRequest) Reset() {
	*x = FraudServiceScoreOrderRequest{}
	mi := &file_gametime_protos_mle_fraud_v1_service_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FraudServiceScoreOrderRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FraudServiceScoreOrderRequest) ProtoMessage() {}

func (x *FraudServiceScoreOrderRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mle_fraud_v1_service_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FraudServiceScoreOrderRequest.ProtoReflect.Descriptor instead.
func (*FraudServiceScoreOrderRequest) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mle_fraud_v1_service_proto_rawDescGZIP(), []int{11}
}

func (x *FraudServiceScoreOrderRequest) GetUser() *UserData {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *FraudServiceScoreOrderRequest) GetDevice() *DeviceData {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *FraudServiceScoreOrderRequest) GetEvent() *EventData {
	if x != nil {
		return x.Event
	}
	return nil
}

func (x *FraudServiceScoreOrderRequest) GetOrder() *OrderData {
	if x != nil {
		return x.Order
	}
	return nil
}

func (x *FraudServiceScoreOrderRequest) GetPaymentMethod() *PaymentMethodData {
	if x != nil {
		return x.PaymentMethod
	}
	return nil
}

func (x *FraudServiceScoreOrderRequest) GetEmailage() *EmailageData {
	if x != nil {
		return x.Emailage
	}
	return nil
}

func (x *FraudServiceScoreOrderRequest) GetTelesign() *TelesignData {
	if x != nil {
		return x.Telesign
	}
	return nil
}

func (x *FraudServiceScoreOrderRequest) GetFastly() *FastlyData {
	if x != nil {
		return x.Fastly
	}
	return nil
}

func (x *FraudServiceScoreOrderRequest) GetTransfer() *TransferData {
	if x != nil {
		return x.Transfer
	}
	return nil
}

type FraudServiceScoreOrderResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Score         float32                `protobuf:"fixed32,1,opt,name=score,proto3" json:"score,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FraudServiceScoreOrderResponse) Reset() {
	*x = FraudServiceScoreOrderResponse{}
	mi := &file_gametime_protos_mle_fraud_v1_service_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FraudServiceScoreOrderResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FraudServiceScoreOrderResponse) ProtoMessage() {}

func (x *FraudServiceScoreOrderResponse) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mle_fraud_v1_service_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FraudServiceScoreOrderResponse.ProtoReflect.Descriptor instead.
func (*FraudServiceScoreOrderResponse) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mle_fraud_v1_service_proto_rawDescGZIP(), []int{12}
}

func (x *FraudServiceScoreOrderResponse) GetScore() float32 {
	if x != nil {
		return x.Score
	}
	return 0
}

var File_gametime_protos_mle_fraud_v1_service_proto protoreflect.FileDescriptor

const file_gametime_protos_mle_fraud_v1_service_proto_rawDesc = "" +
	"\n" +
	"*gametime_protos/mle/fraud/v1/service.proto\x12\fmle.fraud.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x1egoogle/protobuf/wrappers.proto\"\xf2\x03\n" +
	"\bUserData\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x129\n" +
	"\n" +
	"created_at\x18\x02 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x120\n" +
	"\x04name\x18\x03 \x01(\v2\x1c.google.protobuf.StringValueR\x04name\x12\x14\n" +
	"\x05email\x18\x04 \x01(\tR\x05email\x122\n" +
	"\x05phone\x18\x05 \x01(\v2\x1c.google.protobuf.StringValueR\x05phone\x12T\n" +
	"\x18seconds_since_last_order\x18\x06 \x01(\v2\x1b.google.protobuf.Int64ValueR\x15secondsSinceLastOrder\x120\n" +
	"\x14payment_method_count\x18\a \x01(\x05R\x12paymentMethodCount\x12\x1f\n" +
	"\vorder_count\x18\b \x01(\x05R\n" +
	"orderCount\x12;\n" +
	"\n" +
	"first_name\x18\t \x01(\v2\x1c.google.protobuf.StringValueR\tfirstName\x129\n" +
	"\tlast_name\x18\n" +
	" \x01(\v2\x1c.google.protobuf.StringValueR\blastName\"\xd0\x02\n" +
	"\n" +
	"DeviceData\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\"\n" +
	"\fmanufacturer\x18\x02 \x01(\tR\fmanufacturer\x12\x14\n" +
	"\x05model\x18\x03 \x01(\tR\x05model\x12\x17\n" +
	"\aos_type\x18\x04 \x01(\tR\x06osType\x12\x1d\n" +
	"\n" +
	"os_version\x18\x05 \x01(\tR\tosVersion\x12\x1a\n" +
	"\bplatform\x18\x06 \x01(\tR\bplatform\x12\x1f\n" +
	"\vapp_version\x18\a \x01(\tR\n" +
	"appVersion\x12E\n" +
	"\x0faccept_language\x18\b \x01(\v2\x1c.google.protobuf.StringValueR\x0eacceptLanguage\x12\x1d\n" +
	"\n" +
	"ip_address\x18\t \x01(\tR\tipAddress\x12\x1d\n" +
	"\n" +
	"user_agent\x18\n" +
	" \x01(\tR\tuserAgent\"\xd3\x03\n" +
	"\vAddressData\x122\n" +
	"\x05line1\x18\x01 \x01(\v2\x1c.google.protobuf.StringValueR\x05line1\x122\n" +
	"\x05line2\x18\x02 \x01(\v2\x1c.google.protobuf.StringValueR\x05line2\x120\n" +
	"\x04city\x18\x03 \x01(\v2\x1c.google.protobuf.StringValueR\x04city\x122\n" +
	"\x05state\x18\x04 \x01(\v2\x1c.google.protobuf.StringValueR\x05state\x12=\n" +
	"\vpostal_code\x18\x05 \x01(\v2\x1c.google.protobuf.StringValueR\n" +
	"postalCode\x12?\n" +
	"\fcountry_code\x18\x06 \x01(\v2\x1c.google.protobuf.StringValueR\vcountryCode\x12;\n" +
	"\n" +
	"first_name\x18\a \x01(\v2\x1c.google.protobuf.StringValueR\tfirstName\x129\n" +
	"\tlast_name\x18\b \x01(\v2\x1c.google.protobuf.StringValueR\blastName\"\xb0\x05\n" +
	"\tEventData\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x128\n" +
	"\ttimestamp\x18\x02 \x01(\v2\x1a.google.protobuf.TimestampR\ttimestamp\x12\x16\n" +
	"\x06league\x18\x03 \x01(\tR\x06league\x123\n" +
	"\aaddress\x18\x04 \x01(\v2\x19.mle.fraud.v1.AddressDataR\aaddress\x12i\n" +
	"#market_sales_trailing_overall_cents\x18\x05 \x01(\v2\x1b.google.protobuf.Int64ValueR\x1fmarketSalesTrailingOverallCents\x12j\n" +
	"$market_sales_trailing_24_hours_cents\x18\x06 \x01(\v2\x1b.google.protobuf.Int64ValueR\x1fmarketSalesTrailing24HoursCents\x12|\n" +
	"-performer_market_sales_trailing_overall_cents\x18\a \x01(\v2\x1b.google.protobuf.Int64ValueR(performerMarketSalesTrailingOverallCents\x12}\n" +
	".performer_market_sales_trailing_24_hours_cents\x18\b \x01(\v2\x1b.google.protobuf.Int64ValueR(performerMarketSalesTrailing24HoursCents\x12\x1a\n" +
	"\blatitude\x18\t \x01(\x02R\blatitude\x12\x1c\n" +
	"\tlongitude\x18\n" +
	" \x01(\x02R\tlongitude\"\xe3\x02\n" +
	"\tOrderData\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x129\n" +
	"\n" +
	"created_at\x18\x02 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x12\x1a\n" +
	"\bquantity\x18\x03 \x01(\x05R\bquantity\x12#\n" +
	"\rdelivery_type\x18\x04 \x01(\tR\fdeliveryType\x12G\n" +
	"\x0etransfer_email\x18\x05 \x01(\v2\x1c.google.protobuf.StringValueB\x02\x18\x01R\rtransferEmail\x12!\n" +
	"\famount_cents\x18\x06 \x01(\x03R\vamountCents\x12,\n" +
	"\x12credits_used_cents\x18\a \x01(\x03R\x10creditsUsedCents\x120\n" +
	"\x14failed_attempt_count\x18\b \x01(\x05R\x12failedAttemptCount\"\xee\x01\n" +
	"\fTransferData\x122\n" +
	"\x05email\x18\x01 \x01(\v2\x1c.google.protobuf.StringValueR\x05email\x12;\n" +
	"\n" +
	"first_name\x18\x02 \x01(\v2\x1c.google.protobuf.StringValueR\tfirstName\x129\n" +
	"\tlast_name\x18\x03 \x01(\v2\x1c.google.protobuf.StringValueR\blastName\x122\n" +
	"\x05phone\x18\x04 \x01(\v2\x1c.google.protobuf.StringValueR\x05phone\"\xfe\x01\n" +
	"\bCardData\x12\x14\n" +
	"\x05brand\x18\x01 \x01(\tR\x05brand\x12\x10\n" +
	"\x03bin\x18\x02 \x01(\tR\x03bin\x12 \n" +
	"\vfingerprint\x18\x03 \x01(\tR\vfingerprint\x12\x18\n" +
	"\afunding\x18\x04 \x01(\tR\afunding\x129\n" +
	"\tavs_check\x18\x05 \x01(\v2\x1c.google.protobuf.StringValueR\bavsCheck\x129\n" +
	"\tcvv_check\x18\x06 \x01(\v2\x1c.google.protobuf.StringValueR\bcvvCheck\x12\x18\n" +
	"\aprepaid\x18\a \x01(\bR\aprepaid\"\xed\x01\n" +
	"\x11PaymentMethodData\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x129\n" +
	"\n" +
	"created_at\x18\x02 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x12\x18\n" +
	"\agateway\x18\x03 \x01(\tR\agateway\x12\x12\n" +
	"\x04kind\x18\x04 \x01(\tR\x04kind\x12*\n" +
	"\x04card\x18\x05 \x01(\v2\x16.mle.fraud.v1.CardDataR\x04card\x123\n" +
	"\aaddress\x18\x06 \x01(\v2\x19.mle.fraud.v1.AddressDataR\aaddress\"\x85\x01\n" +
	"\fEmailageData\x12\x14\n" +
	"\x05score\x18\x01 \x01(\x05R\x05score\x12&\n" +
	"\x0femail_age_years\x18\x02 \x01(\x05R\remailAgeYears\x12\x1f\n" +
	"\vfraud_level\x18\x03 \x01(\tR\n" +
	"fraudLevel\x12\x16\n" +
	"\x06reason\x18\x04 \x01(\tR\x06reason\"\x7f\n" +
	"\fTelesignData\x12\x1d\n" +
	"\n" +
	"phone_type\x18\x01 \x01(\tR\tphoneType\x12\x14\n" +
	"\x05score\x18\x02 \x01(\x05R\x05score\x12\x12\n" +
	"\x04risk\x18\x03 \x01(\tR\x04risk\x12&\n" +
	"\x0erecommendation\x18\x04 \x01(\tR\x0erecommendation\"\xaa\x02\n" +
	"\n" +
	"FastlyData\x12\x1f\n" +
	"\vpostal_code\x18\x01 \x01(\tR\n" +
	"postalCode\x12!\n" +
	"\fcountry_code\x18\x02 \x01(\tR\vcountryCode\x12\x1a\n" +
	"\blatitude\x18\x03 \x01(\x02R\blatitude\x12\x1c\n" +
	"\tlongitude\x18\x04 \x01(\x02R\tlongitude\x12)\n" +
	"\x10connection_speed\x18\x05 \x01(\tR\x0fconnectionSpeed\x12'\n" +
	"\x0fconnection_type\x18\x06 \x01(\tR\x0econnectionType\x12\x1d\n" +
	"\n" +
	"proxy_type\x18\a \x01(\tR\tproxyType\x12+\n" +
	"\x11proxy_description\x18\b \x01(\tR\x10proxyDescription\"\xfd\x03\n" +
	"\x1dFraudServiceScoreOrderRequest\x12*\n" +
	"\x04user\x18\x01 \x01(\v2\x16.mle.fraud.v1.UserDataR\x04user\x120\n" +
	"\x06device\x18\x02 \x01(\v2\x18.mle.fraud.v1.DeviceDataR\x06device\x12-\n" +
	"\x05event\x18\x03 \x01(\v2\x17.mle.fraud.v1.EventDataR\x05event\x12-\n" +
	"\x05order\x18\x04 \x01(\v2\x17.mle.fraud.v1.OrderDataR\x05order\x12F\n" +
	"\x0epayment_method\x18\x05 \x01(\v2\x1f.mle.fraud.v1.PaymentMethodDataR\rpaymentMethod\x126\n" +
	"\bemailage\x18\x06 \x01(\v2\x1a.mle.fraud.v1.EmailageDataR\bemailage\x126\n" +
	"\btelesign\x18\a \x01(\v2\x1a.mle.fraud.v1.TelesignDataR\btelesign\x120\n" +
	"\x06fastly\x18\b \x01(\v2\x18.mle.fraud.v1.FastlyDataR\x06fastly\x126\n" +
	"\btransfer\x18\t \x01(\v2\x1a.mle.fraud.v1.TransferDataR\btransfer\"6\n" +
	"\x1eFraudServiceScoreOrderResponse\x12\x14\n" +
	"\x05score\x18\x01 \x01(\x02R\x05score2y\n" +
	"\fFraudService\x12i\n" +
	"\n" +
	"ScoreOrder\x12+.mle.fraud.v1.FraudServiceScoreOrderRequest\x1a,.mle.fraud.v1.FraudServiceScoreOrderResponse\"\x00B\x87\x01\n" +
	"\x10com.mle.fraud.v1B\fServiceProtoP\x01Z\x13mle/fraud/v1;protos\xa2\x02\x03MFX\xaa\x02\fMle.Fraud.V1\xca\x02\fMle\\Fraud\\V1\xe2\x02\x18Mle\\Fraud\\V1\\GPBMetadata\xea\x02\x0eMle::Fraud::V1b\x06proto3"

var (
	file_gametime_protos_mle_fraud_v1_service_proto_rawDescOnce sync.Once
	file_gametime_protos_mle_fraud_v1_service_proto_rawDescData []byte
)

func file_gametime_protos_mle_fraud_v1_service_proto_rawDescGZIP() []byte {
	file_gametime_protos_mle_fraud_v1_service_proto_rawDescOnce.Do(func() {
		file_gametime_protos_mle_fraud_v1_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_gametime_protos_mle_fraud_v1_service_proto_rawDesc), len(file_gametime_protos_mle_fraud_v1_service_proto_rawDesc)))
	})
	return file_gametime_protos_mle_fraud_v1_service_proto_rawDescData
}

var file_gametime_protos_mle_fraud_v1_service_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_gametime_protos_mle_fraud_v1_service_proto_goTypes = []any{
	(*UserData)(nil),                       // 0: mle.fraud.v1.UserData
	(*DeviceData)(nil),                     // 1: mle.fraud.v1.DeviceData
	(*AddressData)(nil),                    // 2: mle.fraud.v1.AddressData
	(*EventData)(nil),                      // 3: mle.fraud.v1.EventData
	(*OrderData)(nil),                      // 4: mle.fraud.v1.OrderData
	(*TransferData)(nil),                   // 5: mle.fraud.v1.TransferData
	(*CardData)(nil),                       // 6: mle.fraud.v1.CardData
	(*PaymentMethodData)(nil),              // 7: mle.fraud.v1.PaymentMethodData
	(*EmailageData)(nil),                   // 8: mle.fraud.v1.EmailageData
	(*TelesignData)(nil),                   // 9: mle.fraud.v1.TelesignData
	(*FastlyData)(nil),                     // 10: mle.fraud.v1.FastlyData
	(*FraudServiceScoreOrderRequest)(nil),  // 11: mle.fraud.v1.FraudServiceScoreOrderRequest
	(*FraudServiceScoreOrderResponse)(nil), // 12: mle.fraud.v1.FraudServiceScoreOrderResponse
	(*timestamppb.Timestamp)(nil),          // 13: google.protobuf.Timestamp
	(*wrapperspb.StringValue)(nil),         // 14: google.protobuf.StringValue
	(*wrapperspb.Int64Value)(nil),          // 15: google.protobuf.Int64Value
}
var file_gametime_protos_mle_fraud_v1_service_proto_depIdxs = []int32{
	13, // 0: mle.fraud.v1.UserData.created_at:type_name -> google.protobuf.Timestamp
	14, // 1: mle.fraud.v1.UserData.name:type_name -> google.protobuf.StringValue
	14, // 2: mle.fraud.v1.UserData.phone:type_name -> google.protobuf.StringValue
	15, // 3: mle.fraud.v1.UserData.seconds_since_last_order:type_name -> google.protobuf.Int64Value
	14, // 4: mle.fraud.v1.UserData.first_name:type_name -> google.protobuf.StringValue
	14, // 5: mle.fraud.v1.UserData.last_name:type_name -> google.protobuf.StringValue
	14, // 6: mle.fraud.v1.DeviceData.accept_language:type_name -> google.protobuf.StringValue
	14, // 7: mle.fraud.v1.AddressData.line1:type_name -> google.protobuf.StringValue
	14, // 8: mle.fraud.v1.AddressData.line2:type_name -> google.protobuf.StringValue
	14, // 9: mle.fraud.v1.AddressData.city:type_name -> google.protobuf.StringValue
	14, // 10: mle.fraud.v1.AddressData.state:type_name -> google.protobuf.StringValue
	14, // 11: mle.fraud.v1.AddressData.postal_code:type_name -> google.protobuf.StringValue
	14, // 12: mle.fraud.v1.AddressData.country_code:type_name -> google.protobuf.StringValue
	14, // 13: mle.fraud.v1.AddressData.first_name:type_name -> google.protobuf.StringValue
	14, // 14: mle.fraud.v1.AddressData.last_name:type_name -> google.protobuf.StringValue
	13, // 15: mle.fraud.v1.EventData.timestamp:type_name -> google.protobuf.Timestamp
	2,  // 16: mle.fraud.v1.EventData.address:type_name -> mle.fraud.v1.AddressData
	15, // 17: mle.fraud.v1.EventData.market_sales_trailing_overall_cents:type_name -> google.protobuf.Int64Value
	15, // 18: mle.fraud.v1.EventData.market_sales_trailing_24_hours_cents:type_name -> google.protobuf.Int64Value
	15, // 19: mle.fraud.v1.EventData.performer_market_sales_trailing_overall_cents:type_name -> google.protobuf.Int64Value
	15, // 20: mle.fraud.v1.EventData.performer_market_sales_trailing_24_hours_cents:type_name -> google.protobuf.Int64Value
	13, // 21: mle.fraud.v1.OrderData.created_at:type_name -> google.protobuf.Timestamp
	14, // 22: mle.fraud.v1.OrderData.transfer_email:type_name -> google.protobuf.StringValue
	14, // 23: mle.fraud.v1.TransferData.email:type_name -> google.protobuf.StringValue
	14, // 24: mle.fraud.v1.TransferData.first_name:type_name -> google.protobuf.StringValue
	14, // 25: mle.fraud.v1.TransferData.last_name:type_name -> google.protobuf.StringValue
	14, // 26: mle.fraud.v1.TransferData.phone:type_name -> google.protobuf.StringValue
	14, // 27: mle.fraud.v1.CardData.avs_check:type_name -> google.protobuf.StringValue
	14, // 28: mle.fraud.v1.CardData.cvv_check:type_name -> google.protobuf.StringValue
	13, // 29: mle.fraud.v1.PaymentMethodData.created_at:type_name -> google.protobuf.Timestamp
	6,  // 30: mle.fraud.v1.PaymentMethodData.card:type_name -> mle.fraud.v1.CardData
	2,  // 31: mle.fraud.v1.PaymentMethodData.address:type_name -> mle.fraud.v1.AddressData
	0,  // 32: mle.fraud.v1.FraudServiceScoreOrderRequest.user:type_name -> mle.fraud.v1.UserData
	1,  // 33: mle.fraud.v1.FraudServiceScoreOrderRequest.device:type_name -> mle.fraud.v1.DeviceData
	3,  // 34: mle.fraud.v1.FraudServiceScoreOrderRequest.event:type_name -> mle.fraud.v1.EventData
	4,  // 35: mle.fraud.v1.FraudServiceScoreOrderRequest.order:type_name -> mle.fraud.v1.OrderData
	7,  // 36: mle.fraud.v1.FraudServiceScoreOrderRequest.payment_method:type_name -> mle.fraud.v1.PaymentMethodData
	8,  // 37: mle.fraud.v1.FraudServiceScoreOrderRequest.emailage:type_name -> mle.fraud.v1.EmailageData
	9,  // 38: mle.fraud.v1.FraudServiceScoreOrderRequest.telesign:type_name -> mle.fraud.v1.TelesignData
	10, // 39: mle.fraud.v1.FraudServiceScoreOrderRequest.fastly:type_name -> mle.fraud.v1.FastlyData
	5,  // 40: mle.fraud.v1.FraudServiceScoreOrderRequest.transfer:type_name -> mle.fraud.v1.TransferData
	11, // 41: mle.fraud.v1.FraudService.ScoreOrder:input_type -> mle.fraud.v1.FraudServiceScoreOrderRequest
	12, // 42: mle.fraud.v1.FraudService.ScoreOrder:output_type -> mle.fraud.v1.FraudServiceScoreOrderResponse
	42, // [42:43] is the sub-list for method output_type
	41, // [41:42] is the sub-list for method input_type
	41, // [41:41] is the sub-list for extension type_name
	41, // [41:41] is the sub-list for extension extendee
	0,  // [0:41] is the sub-list for field type_name
}

func init() { file_gametime_protos_mle_fraud_v1_service_proto_init() }
func file_gametime_protos_mle_fraud_v1_service_proto_init() {
	if File_gametime_protos_mle_fraud_v1_service_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_gametime_protos_mle_fraud_v1_service_proto_rawDesc), len(file_gametime_protos_mle_fraud_v1_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_gametime_protos_mle_fraud_v1_service_proto_goTypes,
		DependencyIndexes: file_gametime_protos_mle_fraud_v1_service_proto_depIdxs,
		MessageInfos:      file_gametime_protos_mle_fraud_v1_service_proto_msgTypes,
	}.Build()
	File_gametime_protos_mle_fraud_v1_service_proto = out.File
	file_gametime_protos_mle_fraud_v1_service_proto_goTypes = nil
	file_gametime_protos_mle_fraud_v1_service_proto_depIdxs = nil
}
