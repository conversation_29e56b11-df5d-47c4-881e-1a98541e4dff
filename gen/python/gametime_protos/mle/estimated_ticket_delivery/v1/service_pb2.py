# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: gametime_protos/mle/estimated_ticket_delivery/v1/service.proto
# Protobuf Python Version: 5.29.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    29,
    1,
    '',
    'gametime_protos/mle/estimated_ticket_delivery/v1/service.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n>gametime_protos/mle/estimated_ticket_delivery/v1/service.proto\x12\x19mle.estimated_delivery.v1\":\n\x1bGetEstimatedDeliveryRequest\x12\x1b\n\tticket_id\x18\x01 \x01(\tR\x08ticketId\"V\n\x1cGetEstimatedDeliveryResponse\x12\x36\n\x17\x65stimated_delivery_time\x18\x01 \x01(\x05R\x15\x65stimatedDeliveryTime2\xaa\x01\n\x1e\x45stimatedTicketDeliveryService\x12\x87\x01\n\x14GetEstimatedDelivery\x12\x36.mle.estimated_delivery.v1.GetEstimatedDeliveryRequest\x1a\x37.mle.estimated_delivery.v1.GetEstimatedDeliveryResponseB\xd1\x01\n\x1d\x63om.mle.estimated_delivery.v1B\x0cServiceProtoP\x01Z mle/estimated_delivery/v1;protos\xa2\x02\x03MEX\xaa\x02\x18Mle.EstimatedDelivery.V1\xca\x02\x18Mle\\EstimatedDelivery\\V1\xe2\x02$Mle\\EstimatedDelivery\\V1\\GPBMetadata\xea\x02\x1aMle::EstimatedDelivery::V1b\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'gametime_protos.mle.estimated_ticket_delivery.v1.service_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\035com.mle.estimated_delivery.v1B\014ServiceProtoP\001Z mle/estimated_delivery/v1;protos\242\002\003MEX\252\002\030Mle.EstimatedDelivery.V1\312\002\030Mle\\EstimatedDelivery\\V1\342\002$Mle\\EstimatedDelivery\\V1\\GPBMetadata\352\002\032Mle::EstimatedDelivery::V1'
  _globals['_GETESTIMATEDDELIVERYREQUEST']._serialized_start=93
  _globals['_GETESTIMATEDDELIVERYREQUEST']._serialized_end=151
  _globals['_GETESTIMATEDDELIVERYRESPONSE']._serialized_start=153
  _globals['_GETESTIMATEDDELIVERYRESPONSE']._serialized_end=239
  _globals['_ESTIMATEDTICKETDELIVERYSERVICE']._serialized_start=242
  _globals['_ESTIMATEDTICKETDELIVERYSERVICE']._serialized_end=412
# @@protoc_insertion_point(module_scope)
