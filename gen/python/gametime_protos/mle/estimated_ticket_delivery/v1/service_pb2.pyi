from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Optional as _Optional

DESCRIPTOR: _descriptor.FileDescriptor

class GetEstimatedDeliveryRequest(_message.Message):
    __slots__ = ("ticket_id",)
    TICKET_ID_FIELD_NUMBER: _ClassVar[int]
    ticket_id: str
    def __init__(self, ticket_id: _Optional[str] = ...) -> None: ...

class GetEstimatedDeliveryResponse(_message.Message):
    __slots__ = ("estimated_delivery_time",)
    ESTIMATED_DELIVERY_TIME_FIELD_NUMBER: _ClassVar[int]
    estimated_delivery_time: int
    def __init__(self, estimated_delivery_time: _Optional[int] = ...) -> None: ...
