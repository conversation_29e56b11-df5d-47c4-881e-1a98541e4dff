# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from gametime_protos.mle.estimated_ticket_delivery.v1 import service_pb2 as gametime__protos_dot_mle_dot_estimated__ticket__delivery_dot_v1_dot_service__pb2


class EstimatedTicketDeliveryServiceStub(object):
    """Define a new service for estimated ticket delivery
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetEstimatedDelivery = channel.unary_unary(
                '/mle.estimated_delivery.v1.EstimatedTicketDeliveryService/GetEstimatedDelivery',
                request_serializer=gametime__protos_dot_mle_dot_estimated__ticket__delivery_dot_v1_dot_service__pb2.GetEstimatedDeliveryRequest.SerializeToString,
                response_deserializer=gametime__protos_dot_mle_dot_estimated__ticket__delivery_dot_v1_dot_service__pb2.GetEstimatedDeliveryResponse.FromString,
                _registered_method=True)


class EstimatedTicketDeliveryServiceServicer(object):
    """Define a new service for estimated ticket delivery
    """

    def GetEstimatedDelivery(self, request, context):
        """GetEstimatedDelivery returns the estimated delivery time for a ticket.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_EstimatedTicketDeliveryServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetEstimatedDelivery': grpc.unary_unary_rpc_method_handler(
                    servicer.GetEstimatedDelivery,
                    request_deserializer=gametime__protos_dot_mle_dot_estimated__ticket__delivery_dot_v1_dot_service__pb2.GetEstimatedDeliveryRequest.FromString,
                    response_serializer=gametime__protos_dot_mle_dot_estimated__ticket__delivery_dot_v1_dot_service__pb2.GetEstimatedDeliveryResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'mle.estimated_delivery.v1.EstimatedTicketDeliveryService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('mle.estimated_delivery.v1.EstimatedTicketDeliveryService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class EstimatedTicketDeliveryService(object):
    """Define a new service for estimated ticket delivery
    """

    @staticmethod
    def GetEstimatedDelivery(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/mle.estimated_delivery.v1.EstimatedTicketDeliveryService/GetEstimatedDelivery',
            gametime__protos_dot_mle_dot_estimated__ticket__delivery_dot_v1_dot_service__pb2.GetEstimatedDeliveryRequest.SerializeToString,
            gametime__protos_dot_mle_dot_estimated__ticket__delivery_dot_v1_dot_service__pb2.GetEstimatedDeliveryResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
