# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: gametime_protos/mle/fraud/v1/service.proto
# Protobuf Python Version: 5.29.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    29,
    1,
    '',
    'gametime_protos/mle/fraud/v1/service.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2
from google.protobuf import wrappers_pb2 as google_dot_protobuf_dot_wrappers__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n*gametime_protos/mle/fraud/v1/service.proto\x12\x0cmle.fraud.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x1egoogle/protobuf/wrappers.proto\"\xf2\x03\n\x08UserData\x12\x0e\n\x02id\x18\x01 \x01(\tR\x02id\x12\x39\n\ncreated_at\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.TimestampR\tcreatedAt\x12\x30\n\x04name\x18\x03 \x01(\x0b\x32\x1c.google.protobuf.StringValueR\x04name\x12\x14\n\x05\x65mail\x18\x04 \x01(\tR\x05\x65mail\x12\x32\n\x05phone\x18\x05 \x01(\x0b\x32\x1c.google.protobuf.StringValueR\x05phone\x12T\n\x18seconds_since_last_order\x18\x06 \x01(\x0b\x32\x1b.google.protobuf.Int64ValueR\x15secondsSinceLastOrder\x12\x30\n\x14payment_method_count\x18\x07 \x01(\x05R\x12paymentMethodCount\x12\x1f\n\x0border_count\x18\x08 \x01(\x05R\norderCount\x12;\n\nfirst_name\x18\t \x01(\x0b\x32\x1c.google.protobuf.StringValueR\tfirstName\x12\x39\n\tlast_name\x18\n \x01(\x0b\x32\x1c.google.protobuf.StringValueR\x08lastName\"\xd0\x02\n\nDeviceData\x12\x0e\n\x02id\x18\x01 \x01(\tR\x02id\x12\"\n\x0cmanufacturer\x18\x02 \x01(\tR\x0cmanufacturer\x12\x14\n\x05model\x18\x03 \x01(\tR\x05model\x12\x17\n\x07os_type\x18\x04 \x01(\tR\x06osType\x12\x1d\n\nos_version\x18\x05 \x01(\tR\tosVersion\x12\x1a\n\x08platform\x18\x06 \x01(\tR\x08platform\x12\x1f\n\x0b\x61pp_version\x18\x07 \x01(\tR\nappVersion\x12\x45\n\x0f\x61\x63\x63\x65pt_language\x18\x08 \x01(\x0b\x32\x1c.google.protobuf.StringValueR\x0e\x61\x63\x63\x65ptLanguage\x12\x1d\n\nip_address\x18\t \x01(\tR\tipAddress\x12\x1d\n\nuser_agent\x18\n \x01(\tR\tuserAgent\"\xd3\x03\n\x0b\x41\x64\x64ressData\x12\x32\n\x05line1\x18\x01 \x01(\x0b\x32\x1c.google.protobuf.StringValueR\x05line1\x12\x32\n\x05line2\x18\x02 \x01(\x0b\x32\x1c.google.protobuf.StringValueR\x05line2\x12\x30\n\x04\x63ity\x18\x03 \x01(\x0b\x32\x1c.google.protobuf.StringValueR\x04\x63ity\x12\x32\n\x05state\x18\x04 \x01(\x0b\x32\x1c.google.protobuf.StringValueR\x05state\x12=\n\x0bpostal_code\x18\x05 \x01(\x0b\x32\x1c.google.protobuf.StringValueR\npostalCode\x12?\n\x0c\x63ountry_code\x18\x06 \x01(\x0b\x32\x1c.google.protobuf.StringValueR\x0b\x63ountryCode\x12;\n\nfirst_name\x18\x07 \x01(\x0b\x32\x1c.google.protobuf.StringValueR\tfirstName\x12\x39\n\tlast_name\x18\x08 \x01(\x0b\x32\x1c.google.protobuf.StringValueR\x08lastName\"\xb0\x05\n\tEventData\x12\x0e\n\x02id\x18\x01 \x01(\tR\x02id\x12\x38\n\ttimestamp\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.TimestampR\ttimestamp\x12\x16\n\x06league\x18\x03 \x01(\tR\x06league\x12\x33\n\x07\x61\x64\x64ress\x18\x04 \x01(\x0b\x32\x19.mle.fraud.v1.AddressDataR\x07\x61\x64\x64ress\x12i\n#market_sales_trailing_overall_cents\x18\x05 \x01(\x0b\x32\x1b.google.protobuf.Int64ValueR\x1fmarketSalesTrailingOverallCents\x12j\n$market_sales_trailing_24_hours_cents\x18\x06 \x01(\x0b\x32\x1b.google.protobuf.Int64ValueR\x1fmarketSalesTrailing24HoursCents\x12|\n-performer_market_sales_trailing_overall_cents\x18\x07 \x01(\x0b\x32\x1b.google.protobuf.Int64ValueR(performerMarketSalesTrailingOverallCents\x12}\n.performer_market_sales_trailing_24_hours_cents\x18\x08 \x01(\x0b\x32\x1b.google.protobuf.Int64ValueR(performerMarketSalesTrailing24HoursCents\x12\x1a\n\x08latitude\x18\t \x01(\x02R\x08latitude\x12\x1c\n\tlongitude\x18\n \x01(\x02R\tlongitude\"\xe3\x02\n\tOrderData\x12\x0e\n\x02id\x18\x01 \x01(\tR\x02id\x12\x39\n\ncreated_at\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.TimestampR\tcreatedAt\x12\x1a\n\x08quantity\x18\x03 \x01(\x05R\x08quantity\x12#\n\rdelivery_type\x18\x04 \x01(\tR\x0c\x64\x65liveryType\x12G\n\x0etransfer_email\x18\x05 \x01(\x0b\x32\x1c.google.protobuf.StringValueB\x02\x18\x01R\rtransferEmail\x12!\n\x0c\x61mount_cents\x18\x06 \x01(\x03R\x0b\x61mountCents\x12,\n\x12\x63redits_used_cents\x18\x07 \x01(\x03R\x10\x63reditsUsedCents\x12\x30\n\x14\x66\x61iled_attempt_count\x18\x08 \x01(\x05R\x12\x66\x61iledAttemptCount\"\xee\x01\n\x0cTransferData\x12\x32\n\x05\x65mail\x18\x01 \x01(\x0b\x32\x1c.google.protobuf.StringValueR\x05\x65mail\x12;\n\nfirst_name\x18\x02 \x01(\x0b\x32\x1c.google.protobuf.StringValueR\tfirstName\x12\x39\n\tlast_name\x18\x03 \x01(\x0b\x32\x1c.google.protobuf.StringValueR\x08lastName\x12\x32\n\x05phone\x18\x04 \x01(\x0b\x32\x1c.google.protobuf.StringValueR\x05phone\"\xfe\x01\n\x08\x43\x61rdData\x12\x14\n\x05\x62rand\x18\x01 \x01(\tR\x05\x62rand\x12\x10\n\x03\x62in\x18\x02 \x01(\tR\x03\x62in\x12 \n\x0b\x66ingerprint\x18\x03 \x01(\tR\x0b\x66ingerprint\x12\x18\n\x07\x66unding\x18\x04 \x01(\tR\x07\x66unding\x12\x39\n\tavs_check\x18\x05 \x01(\x0b\x32\x1c.google.protobuf.StringValueR\x08\x61vsCheck\x12\x39\n\tcvv_check\x18\x06 \x01(\x0b\x32\x1c.google.protobuf.StringValueR\x08\x63vvCheck\x12\x18\n\x07prepaid\x18\x07 \x01(\x08R\x07prepaid\"\xed\x01\n\x11PaymentMethodData\x12\x0e\n\x02id\x18\x01 \x01(\tR\x02id\x12\x39\n\ncreated_at\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.TimestampR\tcreatedAt\x12\x18\n\x07gateway\x18\x03 \x01(\tR\x07gateway\x12\x12\n\x04kind\x18\x04 \x01(\tR\x04kind\x12*\n\x04\x63\x61rd\x18\x05 \x01(\x0b\x32\x16.mle.fraud.v1.CardDataR\x04\x63\x61rd\x12\x33\n\x07\x61\x64\x64ress\x18\x06 \x01(\x0b\x32\x19.mle.fraud.v1.AddressDataR\x07\x61\x64\x64ress\"\x85\x01\n\x0c\x45mailageData\x12\x14\n\x05score\x18\x01 \x01(\x05R\x05score\x12&\n\x0f\x65mail_age_years\x18\x02 \x01(\x05R\remailAgeYears\x12\x1f\n\x0b\x66raud_level\x18\x03 \x01(\tR\nfraudLevel\x12\x16\n\x06reason\x18\x04 \x01(\tR\x06reason\"\x7f\n\x0cTelesignData\x12\x1d\n\nphone_type\x18\x01 \x01(\tR\tphoneType\x12\x14\n\x05score\x18\x02 \x01(\x05R\x05score\x12\x12\n\x04risk\x18\x03 \x01(\tR\x04risk\x12&\n\x0erecommendation\x18\x04 \x01(\tR\x0erecommendation\"\xaa\x02\n\nFastlyData\x12\x1f\n\x0bpostal_code\x18\x01 \x01(\tR\npostalCode\x12!\n\x0c\x63ountry_code\x18\x02 \x01(\tR\x0b\x63ountryCode\x12\x1a\n\x08latitude\x18\x03 \x01(\x02R\x08latitude\x12\x1c\n\tlongitude\x18\x04 \x01(\x02R\tlongitude\x12)\n\x10\x63onnection_speed\x18\x05 \x01(\tR\x0f\x63onnectionSpeed\x12\'\n\x0f\x63onnection_type\x18\x06 \x01(\tR\x0e\x63onnectionType\x12\x1d\n\nproxy_type\x18\x07 \x01(\tR\tproxyType\x12+\n\x11proxy_description\x18\x08 \x01(\tR\x10proxyDescription\"\xfd\x03\n\x1d\x46raudServiceScoreOrderRequest\x12*\n\x04user\x18\x01 \x01(\x0b\x32\x16.mle.fraud.v1.UserDataR\x04user\x12\x30\n\x06\x64\x65vice\x18\x02 \x01(\x0b\x32\x18.mle.fraud.v1.DeviceDataR\x06\x64\x65vice\x12-\n\x05\x65vent\x18\x03 \x01(\x0b\x32\x17.mle.fraud.v1.EventDataR\x05\x65vent\x12-\n\x05order\x18\x04 \x01(\x0b\x32\x17.mle.fraud.v1.OrderDataR\x05order\x12\x46\n\x0epayment_method\x18\x05 \x01(\x0b\x32\x1f.mle.fraud.v1.PaymentMethodDataR\rpaymentMethod\x12\x36\n\x08\x65mailage\x18\x06 \x01(\x0b\x32\x1a.mle.fraud.v1.EmailageDataR\x08\x65mailage\x12\x36\n\x08telesign\x18\x07 \x01(\x0b\x32\x1a.mle.fraud.v1.TelesignDataR\x08telesign\x12\x30\n\x06\x66\x61stly\x18\x08 \x01(\x0b\x32\x18.mle.fraud.v1.FastlyDataR\x06\x66\x61stly\x12\x36\n\x08transfer\x18\t \x01(\x0b\x32\x1a.mle.fraud.v1.TransferDataR\x08transfer\"6\n\x1e\x46raudServiceScoreOrderResponse\x12\x14\n\x05score\x18\x01 \x01(\x02R\x05score2y\n\x0c\x46raudService\x12i\n\nScoreOrder\x12+.mle.fraud.v1.FraudServiceScoreOrderRequest\x1a,.mle.fraud.v1.FraudServiceScoreOrderResponse\"\x00\x42\x87\x01\n\x10\x63om.mle.fraud.v1B\x0cServiceProtoP\x01Z\x13mle/fraud/v1;protos\xa2\x02\x03MFX\xaa\x02\x0cMle.Fraud.V1\xca\x02\x0cMle\\Fraud\\V1\xe2\x02\x18Mle\\Fraud\\V1\\GPBMetadata\xea\x02\x0eMle::Fraud::V1b\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'gametime_protos.mle.fraud.v1.service_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\020com.mle.fraud.v1B\014ServiceProtoP\001Z\023mle/fraud/v1;protos\242\002\003MFX\252\002\014Mle.Fraud.V1\312\002\014Mle\\Fraud\\V1\342\002\030Mle\\Fraud\\V1\\GPBMetadata\352\002\016Mle::Fraud::V1'
  _globals['_ORDERDATA'].fields_by_name['transfer_email']._loaded_options = None
  _globals['_ORDERDATA'].fields_by_name['transfer_email']._serialized_options = b'\030\001'
  _globals['_USERDATA']._serialized_start=126
  _globals['_USERDATA']._serialized_end=624
  _globals['_DEVICEDATA']._serialized_start=627
  _globals['_DEVICEDATA']._serialized_end=963
  _globals['_ADDRESSDATA']._serialized_start=966
  _globals['_ADDRESSDATA']._serialized_end=1433
  _globals['_EVENTDATA']._serialized_start=1436
  _globals['_EVENTDATA']._serialized_end=2124
  _globals['_ORDERDATA']._serialized_start=2127
  _globals['_ORDERDATA']._serialized_end=2482
  _globals['_TRANSFERDATA']._serialized_start=2485
  _globals['_TRANSFERDATA']._serialized_end=2723
  _globals['_CARDDATA']._serialized_start=2726
  _globals['_CARDDATA']._serialized_end=2980
  _globals['_PAYMENTMETHODDATA']._serialized_start=2983
  _globals['_PAYMENTMETHODDATA']._serialized_end=3220
  _globals['_EMAILAGEDATA']._serialized_start=3223
  _globals['_EMAILAGEDATA']._serialized_end=3356
  _globals['_TELESIGNDATA']._serialized_start=3358
  _globals['_TELESIGNDATA']._serialized_end=3485
  _globals['_FASTLYDATA']._serialized_start=3488
  _globals['_FASTLYDATA']._serialized_end=3786
  _globals['_FRAUDSERVICESCOREORDERREQUEST']._serialized_start=3789
  _globals['_FRAUDSERVICESCOREORDERREQUEST']._serialized_end=4298
  _globals['_FRAUDSERVICESCOREORDERRESPONSE']._serialized_start=4300
  _globals['_FRAUDSERVICESCOREORDERRESPONSE']._serialized_end=4354
  _globals['_FRAUDSERVICE']._serialized_start=4356
  _globals['_FRAUDSERVICE']._serialized_end=4477
# @@protoc_insertion_point(module_scope)
