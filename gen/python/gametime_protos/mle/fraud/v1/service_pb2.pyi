from google.protobuf import timestamp_pb2 as _timestamp_pb2
from google.protobuf import wrappers_pb2 as _wrappers_pb2
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Mapping as _Mapping, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class UserData(_message.Message):
    __slots__ = ("id", "created_at", "name", "email", "phone", "seconds_since_last_order", "payment_method_count", "order_count", "first_name", "last_name")
    ID_FIELD_NUMBER: _ClassVar[int]
    CREATED_AT_FIELD_NUMBER: _ClassVar[int]
    NAME_FIELD_NUMBER: _ClassVar[int]
    EMAIL_FIELD_NUMBER: _ClassVar[int]
    PHONE_FIELD_NUMBER: _ClassVar[int]
    SECONDS_SINCE_LAST_ORDER_FIELD_NUMBER: _ClassVar[int]
    PAYMENT_METHOD_COUNT_FIELD_NUMBER: _ClassVar[int]
    ORDER_COUNT_FIELD_NUMBER: _ClassVar[int]
    FIRST_NAME_FIELD_NUMBER: _ClassVar[int]
    LAST_NAME_FIELD_NUMBER: _ClassVar[int]
    id: str
    created_at: _timestamp_pb2.Timestamp
    name: _wrappers_pb2.StringValue
    email: str
    phone: _wrappers_pb2.StringValue
    seconds_since_last_order: _wrappers_pb2.Int64Value
    payment_method_count: int
    order_count: int
    first_name: _wrappers_pb2.StringValue
    last_name: _wrappers_pb2.StringValue
    def __init__(self, id: _Optional[str] = ..., created_at: _Optional[_Union[_timestamp_pb2.Timestamp, _Mapping]] = ..., name: _Optional[_Union[_wrappers_pb2.StringValue, _Mapping]] = ..., email: _Optional[str] = ..., phone: _Optional[_Union[_wrappers_pb2.StringValue, _Mapping]] = ..., seconds_since_last_order: _Optional[_Union[_wrappers_pb2.Int64Value, _Mapping]] = ..., payment_method_count: _Optional[int] = ..., order_count: _Optional[int] = ..., first_name: _Optional[_Union[_wrappers_pb2.StringValue, _Mapping]] = ..., last_name: _Optional[_Union[_wrappers_pb2.StringValue, _Mapping]] = ...) -> None: ...

class DeviceData(_message.Message):
    __slots__ = ("id", "manufacturer", "model", "os_type", "os_version", "platform", "app_version", "accept_language", "ip_address", "user_agent")
    ID_FIELD_NUMBER: _ClassVar[int]
    MANUFACTURER_FIELD_NUMBER: _ClassVar[int]
    MODEL_FIELD_NUMBER: _ClassVar[int]
    OS_TYPE_FIELD_NUMBER: _ClassVar[int]
    OS_VERSION_FIELD_NUMBER: _ClassVar[int]
    PLATFORM_FIELD_NUMBER: _ClassVar[int]
    APP_VERSION_FIELD_NUMBER: _ClassVar[int]
    ACCEPT_LANGUAGE_FIELD_NUMBER: _ClassVar[int]
    IP_ADDRESS_FIELD_NUMBER: _ClassVar[int]
    USER_AGENT_FIELD_NUMBER: _ClassVar[int]
    id: str
    manufacturer: str
    model: str
    os_type: str
    os_version: str
    platform: str
    app_version: str
    accept_language: _wrappers_pb2.StringValue
    ip_address: str
    user_agent: str
    def __init__(self, id: _Optional[str] = ..., manufacturer: _Optional[str] = ..., model: _Optional[str] = ..., os_type: _Optional[str] = ..., os_version: _Optional[str] = ..., platform: _Optional[str] = ..., app_version: _Optional[str] = ..., accept_language: _Optional[_Union[_wrappers_pb2.StringValue, _Mapping]] = ..., ip_address: _Optional[str] = ..., user_agent: _Optional[str] = ...) -> None: ...

class AddressData(_message.Message):
    __slots__ = ("line1", "line2", "city", "state", "postal_code", "country_code", "first_name", "last_name")
    LINE1_FIELD_NUMBER: _ClassVar[int]
    LINE2_FIELD_NUMBER: _ClassVar[int]
    CITY_FIELD_NUMBER: _ClassVar[int]
    STATE_FIELD_NUMBER: _ClassVar[int]
    POSTAL_CODE_FIELD_NUMBER: _ClassVar[int]
    COUNTRY_CODE_FIELD_NUMBER: _ClassVar[int]
    FIRST_NAME_FIELD_NUMBER: _ClassVar[int]
    LAST_NAME_FIELD_NUMBER: _ClassVar[int]
    line1: _wrappers_pb2.StringValue
    line2: _wrappers_pb2.StringValue
    city: _wrappers_pb2.StringValue
    state: _wrappers_pb2.StringValue
    postal_code: _wrappers_pb2.StringValue
    country_code: _wrappers_pb2.StringValue
    first_name: _wrappers_pb2.StringValue
    last_name: _wrappers_pb2.StringValue
    def __init__(self, line1: _Optional[_Union[_wrappers_pb2.StringValue, _Mapping]] = ..., line2: _Optional[_Union[_wrappers_pb2.StringValue, _Mapping]] = ..., city: _Optional[_Union[_wrappers_pb2.StringValue, _Mapping]] = ..., state: _Optional[_Union[_wrappers_pb2.StringValue, _Mapping]] = ..., postal_code: _Optional[_Union[_wrappers_pb2.StringValue, _Mapping]] = ..., country_code: _Optional[_Union[_wrappers_pb2.StringValue, _Mapping]] = ..., first_name: _Optional[_Union[_wrappers_pb2.StringValue, _Mapping]] = ..., last_name: _Optional[_Union[_wrappers_pb2.StringValue, _Mapping]] = ...) -> None: ...

class EventData(_message.Message):
    __slots__ = ("id", "timestamp", "league", "address", "market_sales_trailing_overall_cents", "market_sales_trailing_24_hours_cents", "performer_market_sales_trailing_overall_cents", "performer_market_sales_trailing_24_hours_cents", "latitude", "longitude")
    ID_FIELD_NUMBER: _ClassVar[int]
    TIMESTAMP_FIELD_NUMBER: _ClassVar[int]
    LEAGUE_FIELD_NUMBER: _ClassVar[int]
    ADDRESS_FIELD_NUMBER: _ClassVar[int]
    MARKET_SALES_TRAILING_OVERALL_CENTS_FIELD_NUMBER: _ClassVar[int]
    MARKET_SALES_TRAILING_24_HOURS_CENTS_FIELD_NUMBER: _ClassVar[int]
    PERFORMER_MARKET_SALES_TRAILING_OVERALL_CENTS_FIELD_NUMBER: _ClassVar[int]
    PERFORMER_MARKET_SALES_TRAILING_24_HOURS_CENTS_FIELD_NUMBER: _ClassVar[int]
    LATITUDE_FIELD_NUMBER: _ClassVar[int]
    LONGITUDE_FIELD_NUMBER: _ClassVar[int]
    id: str
    timestamp: _timestamp_pb2.Timestamp
    league: str
    address: AddressData
    market_sales_trailing_overall_cents: _wrappers_pb2.Int64Value
    market_sales_trailing_24_hours_cents: _wrappers_pb2.Int64Value
    performer_market_sales_trailing_overall_cents: _wrappers_pb2.Int64Value
    performer_market_sales_trailing_24_hours_cents: _wrappers_pb2.Int64Value
    latitude: float
    longitude: float
    def __init__(self, id: _Optional[str] = ..., timestamp: _Optional[_Union[_timestamp_pb2.Timestamp, _Mapping]] = ..., league: _Optional[str] = ..., address: _Optional[_Union[AddressData, _Mapping]] = ..., market_sales_trailing_overall_cents: _Optional[_Union[_wrappers_pb2.Int64Value, _Mapping]] = ..., market_sales_trailing_24_hours_cents: _Optional[_Union[_wrappers_pb2.Int64Value, _Mapping]] = ..., performer_market_sales_trailing_overall_cents: _Optional[_Union[_wrappers_pb2.Int64Value, _Mapping]] = ..., performer_market_sales_trailing_24_hours_cents: _Optional[_Union[_wrappers_pb2.Int64Value, _Mapping]] = ..., latitude: _Optional[float] = ..., longitude: _Optional[float] = ...) -> None: ...

class OrderData(_message.Message):
    __slots__ = ("id", "created_at", "quantity", "delivery_type", "transfer_email", "amount_cents", "credits_used_cents", "failed_attempt_count")
    ID_FIELD_NUMBER: _ClassVar[int]
    CREATED_AT_FIELD_NUMBER: _ClassVar[int]
    QUANTITY_FIELD_NUMBER: _ClassVar[int]
    DELIVERY_TYPE_FIELD_NUMBER: _ClassVar[int]
    TRANSFER_EMAIL_FIELD_NUMBER: _ClassVar[int]
    AMOUNT_CENTS_FIELD_NUMBER: _ClassVar[int]
    CREDITS_USED_CENTS_FIELD_NUMBER: _ClassVar[int]
    FAILED_ATTEMPT_COUNT_FIELD_NUMBER: _ClassVar[int]
    id: str
    created_at: _timestamp_pb2.Timestamp
    quantity: int
    delivery_type: str
    transfer_email: _wrappers_pb2.StringValue
    amount_cents: int
    credits_used_cents: int
    failed_attempt_count: int
    def __init__(self, id: _Optional[str] = ..., created_at: _Optional[_Union[_timestamp_pb2.Timestamp, _Mapping]] = ..., quantity: _Optional[int] = ..., delivery_type: _Optional[str] = ..., transfer_email: _Optional[_Union[_wrappers_pb2.StringValue, _Mapping]] = ..., amount_cents: _Optional[int] = ..., credits_used_cents: _Optional[int] = ..., failed_attempt_count: _Optional[int] = ...) -> None: ...

class TransferData(_message.Message):
    __slots__ = ("email", "first_name", "last_name", "phone")
    EMAIL_FIELD_NUMBER: _ClassVar[int]
    FIRST_NAME_FIELD_NUMBER: _ClassVar[int]
    LAST_NAME_FIELD_NUMBER: _ClassVar[int]
    PHONE_FIELD_NUMBER: _ClassVar[int]
    email: _wrappers_pb2.StringValue
    first_name: _wrappers_pb2.StringValue
    last_name: _wrappers_pb2.StringValue
    phone: _wrappers_pb2.StringValue
    def __init__(self, email: _Optional[_Union[_wrappers_pb2.StringValue, _Mapping]] = ..., first_name: _Optional[_Union[_wrappers_pb2.StringValue, _Mapping]] = ..., last_name: _Optional[_Union[_wrappers_pb2.StringValue, _Mapping]] = ..., phone: _Optional[_Union[_wrappers_pb2.StringValue, _Mapping]] = ...) -> None: ...

class CardData(_message.Message):
    __slots__ = ("brand", "bin", "fingerprint", "funding", "avs_check", "cvv_check", "prepaid")
    BRAND_FIELD_NUMBER: _ClassVar[int]
    BIN_FIELD_NUMBER: _ClassVar[int]
    FINGERPRINT_FIELD_NUMBER: _ClassVar[int]
    FUNDING_FIELD_NUMBER: _ClassVar[int]
    AVS_CHECK_FIELD_NUMBER: _ClassVar[int]
    CVV_CHECK_FIELD_NUMBER: _ClassVar[int]
    PREPAID_FIELD_NUMBER: _ClassVar[int]
    brand: str
    bin: str
    fingerprint: str
    funding: str
    avs_check: _wrappers_pb2.StringValue
    cvv_check: _wrappers_pb2.StringValue
    prepaid: bool
    def __init__(self, brand: _Optional[str] = ..., bin: _Optional[str] = ..., fingerprint: _Optional[str] = ..., funding: _Optional[str] = ..., avs_check: _Optional[_Union[_wrappers_pb2.StringValue, _Mapping]] = ..., cvv_check: _Optional[_Union[_wrappers_pb2.StringValue, _Mapping]] = ..., prepaid: bool = ...) -> None: ...

class PaymentMethodData(_message.Message):
    __slots__ = ("id", "created_at", "gateway", "kind", "card", "address")
    ID_FIELD_NUMBER: _ClassVar[int]
    CREATED_AT_FIELD_NUMBER: _ClassVar[int]
    GATEWAY_FIELD_NUMBER: _ClassVar[int]
    KIND_FIELD_NUMBER: _ClassVar[int]
    CARD_FIELD_NUMBER: _ClassVar[int]
    ADDRESS_FIELD_NUMBER: _ClassVar[int]
    id: str
    created_at: _timestamp_pb2.Timestamp
    gateway: str
    kind: str
    card: CardData
    address: AddressData
    def __init__(self, id: _Optional[str] = ..., created_at: _Optional[_Union[_timestamp_pb2.Timestamp, _Mapping]] = ..., gateway: _Optional[str] = ..., kind: _Optional[str] = ..., card: _Optional[_Union[CardData, _Mapping]] = ..., address: _Optional[_Union[AddressData, _Mapping]] = ...) -> None: ...

class EmailageData(_message.Message):
    __slots__ = ("score", "email_age_years", "fraud_level", "reason")
    SCORE_FIELD_NUMBER: _ClassVar[int]
    EMAIL_AGE_YEARS_FIELD_NUMBER: _ClassVar[int]
    FRAUD_LEVEL_FIELD_NUMBER: _ClassVar[int]
    REASON_FIELD_NUMBER: _ClassVar[int]
    score: int
    email_age_years: int
    fraud_level: str
    reason: str
    def __init__(self, score: _Optional[int] = ..., email_age_years: _Optional[int] = ..., fraud_level: _Optional[str] = ..., reason: _Optional[str] = ...) -> None: ...

class TelesignData(_message.Message):
    __slots__ = ("phone_type", "score", "risk", "recommendation")
    PHONE_TYPE_FIELD_NUMBER: _ClassVar[int]
    SCORE_FIELD_NUMBER: _ClassVar[int]
    RISK_FIELD_NUMBER: _ClassVar[int]
    RECOMMENDATION_FIELD_NUMBER: _ClassVar[int]
    phone_type: str
    score: int
    risk: str
    recommendation: str
    def __init__(self, phone_type: _Optional[str] = ..., score: _Optional[int] = ..., risk: _Optional[str] = ..., recommendation: _Optional[str] = ...) -> None: ...

class FastlyData(_message.Message):
    __slots__ = ("postal_code", "country_code", "latitude", "longitude", "connection_speed", "connection_type", "proxy_type", "proxy_description")
    POSTAL_CODE_FIELD_NUMBER: _ClassVar[int]
    COUNTRY_CODE_FIELD_NUMBER: _ClassVar[int]
    LATITUDE_FIELD_NUMBER: _ClassVar[int]
    LONGITUDE_FIELD_NUMBER: _ClassVar[int]
    CONNECTION_SPEED_FIELD_NUMBER: _ClassVar[int]
    CONNECTION_TYPE_FIELD_NUMBER: _ClassVar[int]
    PROXY_TYPE_FIELD_NUMBER: _ClassVar[int]
    PROXY_DESCRIPTION_FIELD_NUMBER: _ClassVar[int]
    postal_code: str
    country_code: str
    latitude: float
    longitude: float
    connection_speed: str
    connection_type: str
    proxy_type: str
    proxy_description: str
    def __init__(self, postal_code: _Optional[str] = ..., country_code: _Optional[str] = ..., latitude: _Optional[float] = ..., longitude: _Optional[float] = ..., connection_speed: _Optional[str] = ..., connection_type: _Optional[str] = ..., proxy_type: _Optional[str] = ..., proxy_description: _Optional[str] = ...) -> None: ...

class FraudServiceScoreOrderRequest(_message.Message):
    __slots__ = ("user", "device", "event", "order", "payment_method", "emailage", "telesign", "fastly", "transfer")
    USER_FIELD_NUMBER: _ClassVar[int]
    DEVICE_FIELD_NUMBER: _ClassVar[int]
    EVENT_FIELD_NUMBER: _ClassVar[int]
    ORDER_FIELD_NUMBER: _ClassVar[int]
    PAYMENT_METHOD_FIELD_NUMBER: _ClassVar[int]
    EMAILAGE_FIELD_NUMBER: _ClassVar[int]
    TELESIGN_FIELD_NUMBER: _ClassVar[int]
    FASTLY_FIELD_NUMBER: _ClassVar[int]
    TRANSFER_FIELD_NUMBER: _ClassVar[int]
    user: UserData
    device: DeviceData
    event: EventData
    order: OrderData
    payment_method: PaymentMethodData
    emailage: EmailageData
    telesign: TelesignData
    fastly: FastlyData
    transfer: TransferData
    def __init__(self, user: _Optional[_Union[UserData, _Mapping]] = ..., device: _Optional[_Union[DeviceData, _Mapping]] = ..., event: _Optional[_Union[EventData, _Mapping]] = ..., order: _Optional[_Union[OrderData, _Mapping]] = ..., payment_method: _Optional[_Union[PaymentMethodData, _Mapping]] = ..., emailage: _Optional[_Union[EmailageData, _Mapping]] = ..., telesign: _Optional[_Union[TelesignData, _Mapping]] = ..., fastly: _Optional[_Union[FastlyData, _Mapping]] = ..., transfer: _Optional[_Union[TransferData, _Mapping]] = ...) -> None: ...

class FraudServiceScoreOrderResponse(_message.Message):
    __slots__ = ("score",)
    SCORE_FIELD_NUMBER: _ClassVar[int]
    score: float
    def __init__(self, score: _Optional[float] = ...) -> None: ...
