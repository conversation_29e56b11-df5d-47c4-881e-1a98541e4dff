[build-system]
build-backend = "hatchling.build"
requires = ["hatchling"]

[project]
name = "gametime_protos"
version = "1.7.0"
description = "Gametime generated protos"
authors = [{name = "Gametime Eng"}]
readme = "README.md"
requires-python = ">=3.10,<4.0.0"
dependencies = [
  "protobuf==5.29.1",
  "types-protobuf==5.29.1.20250315",
  "google-api-core>=2.24.2,<3.0.0",
  "grpcio>=1.71.0,<2.0.0",
]

[tool.hatch.build.targets.wheel]
packages = ["gametime_protos"]

[tool.hatch.metadata]
allow-direct-references = true

[tool.semantic_release]
version_toml = ["pyproject.toml:project.version"]
commit_parser = "conventional"
changelog_file = "CHANGELOG.md"
build_command = ""
upload_to_repository = false
upload_to_release = true
tag_format = "gametime_protos-v{version}"

[tool.semantic_release.branches]
main.match = "main"
